# OpenAPI to MCP Architecture & Function Calling Flow

This document explains the architecture and function calling flow of the OpenAPI to MCP (Model Context Protocol) server generator.

## 🏗️ System Overview

The system converts any OpenAPI specification into a fully functional MCP server with LLM function calling capabilities. It consists of:

- **Main Server** (Port 3000): Handles conversion and server management
- **Generated MCP Servers** (Ports 8000+): Individual servers for each OpenAPI spec
- **Web UI** (Port 3001): React-based interface for easy interaction
- **LiteLLM Gateway** (Port 4000): LLM provider abstraction layer

## 📊 Architecture Components

### Frontend Layer
- **Web UI**: React application for user interaction
- **Direct API**: REST endpoints for programmatic access

### Main Server (Port 3000)
- **Convert API** (`/api/convert`): Converts OpenAPI specs to MCP servers
- **Server Manager**: Manages multiple MCP server instances
- **Instant MCP** (`/api/instant-mcp`): Quick server generation
- **Generation Engine**: 
  - OpenAPI Parser: Validates and parses specifications
  - Server Generator: Creates MCP server code
  - Manifest Generator: Generates MCP manifests

### Generated MCP Servers (Ports 8000+)
Each OpenAPI spec generates a dedicated MCP server with:

#### MCP Protocol Endpoints
- **`/mcp`**: Main protocol handler for tool calls
- **`/tools/*`**: Individual tool endpoints for each OpenAPI operation
- **`/chat`**: LLM integration with function calling support
- **`/health`**: Health check endpoint

#### Function Calling Engine
- **Tool Definitions**: Auto-generated from OpenAPI operations
- **Parameter Validator**: Smart parameter extraction and validation
- **Response Formatter**: User-friendly output formatting

## 🔄 Function Calling Flow

### 1. Server Generation Phase
```
User Input (OpenAPI URL) → Parser → Generator → MCP Server Deployment
```

### 2. Function Calling Phase
```
User Message → LLM Request (with tools) → Tool Call Detection → 
Parameter Extraction → MCP Protocol → Tool Execution → API Call → 
Response Formatting → User Response
```

### 3. Detailed Sequence

1. **User sends chat message**: `"Show me available pets"`
2. **Tool definitions generated**: From OpenAPI spec operations
3. **LLM request with tools**: Sent to LiteLLM Gateway
4. **LLM responds with tool call**:
   ```json
   {
     "tool_calls": [{
       "function": {
         "name": "findPetsByStatus",
         "arguments": "{\"query\":{\"status\":\"available\"}}"
       }
     }]
   }
   ```
5. **Parameter extraction**: Parse and validate arguments
6. **MCP protocol call**: Route through `/mcp` endpoint
7. **Tool execution**: Call actual OpenAPI endpoint
8. **Response formatting**: Format for better UX
9. **User receives response**: Formatted, user-friendly output

## 🔌 MCP Protocol Integration

### Standard Interface
- **Unified endpoint**: All tool calls go through `/mcp`
- **Tool discovery**: Auto-generated from OpenAPI spec
- **Error handling**: Graceful fallbacks and error messages
- **Parameter validation**: Smart parameter extraction and correction

### Tool Definition Format
```typescript
{
  type: "function",
  function: {
    name: "findPetsByStatus",
    description: "Find pets by status",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "object",
          properties: {
            status: {
              type: "string",
              enum: ["available", "pending", "sold"]
            }
          }
        }
      }
    }
  }
}
```

## 🚀 Key Features

### Function Calling
- **Auto tool detection**: LLM automatically chooses appropriate tools
- **Parameter validation**: Smart extraction from user messages
- **Smart routing**: Efficient routing to correct endpoints
- **Error recovery**: Fallback mechanisms for failed calls

### MCP Protocol
- **Standard interface**: Consistent API across all generated servers
- **Tool discovery**: Dynamic tool generation from OpenAPI specs
- **Error handling**: Comprehensive error handling and logging
- **Multi-server support**: Multiple APIs can run simultaneously

### LLM Integration
- **Multiple providers**: Support for OpenAI, Anthropic, etc. via LiteLLM
- **Tool calling support**: Native function calling capabilities
- **Context management**: Intelligent context handling for better responses
- **Response formatting**: User-friendly output formatting

### Multi-Tenant Architecture
- **Multiple APIs**: Support for multiple OpenAPI specifications
- **Isolated servers**: Each API gets its own server instance
- **Dynamic ports**: Automatic port allocation for new servers
- **Resource management**: Efficient resource usage and cleanup

## 🔧 Technical Implementation

### Server Generation
1. **Parse OpenAPI spec**: Validate and extract operations
2. **Generate TypeScript code**: Create Express server with MCP endpoints
3. **Create tool definitions**: Convert operations to LLM-callable functions
4. **Deploy server**: Start new server instance on available port
5. **Register with manager**: Track server for management

### Function Call Processing
1. **Receive chat message**: User input via `/chat` endpoint
2. **Generate tool context**: Create tools array from OpenAPI spec
3. **Send to LLM**: Include tools in LiteLLM request
4. **Process response**: Check for tool calls in LLM response
5. **Execute tool**: Route through MCP protocol to actual API
6. **Format response**: Create user-friendly output

### Error Handling
- **Validation errors**: Parameter validation and correction
- **API errors**: Graceful handling of external API failures
- **Fallback mechanisms**: Direct API calls when MCP fails
- **Logging**: Comprehensive logging for debugging

## 📁 File Structure

```
src/
├── server.ts              # Main server entry point
├── core/
│   ├── openapiParser.ts   # OpenAPI specification parser
│   ├── serverGenerator.ts # MCP server code generator
│   └── mcpManifestGenerator.ts # MCP manifest generator
├── routes/
│   ├── convert.ts         # Conversion API endpoints
│   ├── servers.ts         # Server management endpoints
│   └── instantMcp.ts      # Instant MCP creation
├── services/
│   └── serverManager.ts   # Multi-server management
└── types.ts               # TypeScript type definitions

Generated MCP Servers:
tmp/
└── {server-id}/
    ├── src/
    │   ├── server.ts      # Generated MCP server
    │   ├── routes.ts      # Tool endpoint handlers
    │   └── types.ts       # Generated type definitions
    ├── package.json       # Dependencies
    └── tsconfig.json      # TypeScript configuration
```

## 🌐 External Services

### LiteLLM Gateway (localhost:4000)
- **Purpose**: LLM provider abstraction
- **Supported providers**: OpenAI, Anthropic, Azure, etc.
- **Function calling**: Native tool calling support
- **Configuration**: Via environment variables

### Target APIs
- **Petstore API**: Example OpenAPI service
- **Custom APIs**: Any OpenAPI 3.0+ compliant service
- **Authentication**: Supports various auth methods
- **Rate limiting**: Respects API rate limits

## 🔐 Environment Configuration

```bash
# LiteLLM Configuration
LITELLM_URL=http://localhost:4000/chat/completions
LITELLM_MODEL=gpt-3.5-turbo
LITELLM_API_KEY=your_api_key_here

# Server Configuration
PORT=3000
NODE_ENV=development
CORS_ORIGIN=*

# Generated Server Configuration
BASE_URL=https://petstore3.swagger.io/api/v3
```

## 🚀 Getting Started

1. **Start the main server**: `npm run dev`
2. **Start LiteLLM Gateway**: `litellm --config config.yaml`
3. **Access Web UI**: http://localhost:3001
4. **Generate MCP server**: Enter OpenAPI URL and click generate
5. **Test function calling**: Use the chat interface to interact with your API

## 📝 Example Usage

```bash
# Generate MCP server
curl -X POST http://localhost:3000/api/convert \
  -H "Content-Type: application/json" \
  -d '{"openapi": "https://petstore3.swagger.io/api/v3/openapi.json"}'

# Chat with generated server
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Show me all available pets"}'
```

This architecture enables any OpenAPI service to become an intelligent, LLM-callable tool through the MCP protocol! 🎉
