"use strict";
/**
 * API server entry point
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const path = __importStar(require("path"));
const axios_1 = __importDefault(require("axios"));
const convert_1 = require("./routes/convert");
const download_1 = require("./routes/download");
const errorHandler_1 = require("./middleware/errorHandler");
const instantMcp_1 = __importDefault(require("./routes/instantMcp"));
const servers_1 = require("./routes/servers");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
// Security middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Serve static files (web UI) - check multiple locations
const publicPath = path.join(__dirname, '../public');
const uiDistPath = path.join(__dirname, '../ui/build');
// Try to serve UI build files first, then fallback to public
if (require('fs').existsSync(uiDistPath)) {
    app.use(express_1.default.static(uiDistPath));
}
else if (require('fs').existsSync(publicPath)) {
    app.use(express_1.default.static(publicPath));
}
// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime()
    });
});
// API routes
app.use('/api/convert', convert_1.convertRoutes);
app.use('/api/download', download_1.downloadRoutes);
app.use('/api/instant-mcp', instantMcp_1.default);
app.use('/api/servers', servers_1.serversRoutes);
// --- LLM Chat endpoint using LiteLLM Gateway ---
app.post('/api/chat', async (req, res) => {
    const { message } = req.body;
    if (!message) {
        return res.status(400).json({ error: 'No message provided' });
    }
    const llmUrl = process.env.LITELLM_URL || 'http://localhost:4000/chat/completions';
    const llmModel = process.env.LITELLM_MODEL || 'gpt-3.5-turbo';
    console.log('[API] LLM Chat request:', {
        url: llmUrl,
        model: llmModel,
        apiKeyPresent: !!process.env.LITELLM_API_KEY,
        message
    });
    try {
        // Call LiteLLM gateway (default: http://localhost:4000)
        const llmRes = await axios_1.default.post(llmUrl, {
            model: llmModel,
            messages: [
                { role: 'system', content: 'You are an API assistant. Help the user interact with the API using natural language.' },
                { role: 'user', content: message }
            ]
        });
        const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);
        res.json({ response: llmMessage });
    }
    catch (err) {
        console.error('[API] LLM call failed:', err && (err.response?.data || err.message || err.toString()), err);
        res.status(500).json({ error: 'LLM call failed', details: err && (err.response?.data || err.message || err.toString()) });
    }
});
// Root endpoint - serve web UI or API info
app.get('/', (req, res) => {
    if (req.accepts('html')) {
        // Serve HTML if available, otherwise API info
        res.sendFile(path.join(__dirname, '../public/index.html'), (err) => {
            if (err) {
                res.json({
                    name: 'OpenAPI to MCP Converter',
                    version: process.env.npm_package_version || '1.0.0',
                    description: 'Convert OpenAPI specifications to MCP manifests and servers',
                    endpoints: {
                        '/api/health': 'GET - Health check',
                        '/api/convert': 'POST - Convert OpenAPI to MCP',
                        '/api/download/:bundleId': 'GET - Download generated bundle',
                        '/api/chat': 'POST - Natural language chat with LLM'
                    },
                    documentation: 'https://github.com/yourusername/openapi-to-mcp'
                });
            }
        });
    }
    else {
        res.json({
            name: 'OpenAPI to MCP Converter',
            version: process.env.npm_package_version || '1.0.0',
            description: 'Convert OpenAPI specifications to MCP manifests and servers',
            endpoints: {
                '/api/health': 'GET - Health check',
                '/api/convert': 'POST - Convert OpenAPI to MCP',
                '/api/download/:bundleId': 'GET - Download generated bundle',
                '/api/chat': 'POST - Natural language chat with LLM'
            },
            documentation: 'https://github.com/yourusername/openapi-to-mcp'
        });
    }
});
// Error handling middleware (must be last)
app.use(errorHandler_1.errorHandler);
// Serve index.html for all non-API routes (SPA fallback)
app.get('*', (req, res) => {
    if (!req.path.startsWith('/api/')) {
        res.sendFile(path.join(__dirname, '../public/index.html'));
    }
    else {
        res.status(404).json({
            error: 'Not Found',
            message: `Route ${req.originalUrl} not found`,
            availableEndpoints: [
                'GET /',
                'GET /api/health',
                'POST /api/convert',
                'GET /api/download/:bundleId',
                'POST /api/chat'
            ]
        });
    }
});
// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 OpenAPI to MCP Server running on port ${PORT}`);
    console.log(`📖 API Documentation: http://localhost:${PORT}/`);
    console.log(`🔧 Health Check: http://localhost:${PORT}/api/health`);
    console.log(`💬 Chat endpoint: http://localhost:${PORT}/api/chat`);
    if (process.env.NODE_ENV === 'development') {
        console.log(`📱 Web UI: http://localhost:${PORT}/`);
    }
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    console.log('🛑 SIGINT received, shutting down gracefully');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
exports.default = app;
//# sourceMappingURL=server.js.map