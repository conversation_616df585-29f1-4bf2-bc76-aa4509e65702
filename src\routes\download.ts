/**
 * Download API routes
 */

import { Router, Request, Response, NextFunction } from 'express';
import * as path from 'path';
import { BundleGenerator } from '../core/bundleGenerator';
import { formatBytes } from '../utils';

const router = Router();

// Download bundle by ID
router.get('/:bundleId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { bundleId } = req.params;

    // Validate bundle ID format (UUID with optional timestamp)
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}(-\d+)?$/i;
    if (!uuidRegex.test(bundleId)) {
      return res.status(400).json({
        error: 'Invalid bundle ID',
        message: 'Bundle ID must be a valid UUID format'
      });
    }

    const bundleGenerator = new BundleGenerator();

    // Check if bundle exists
    const exists = await bundleGenerator.bundleExists(bundleId);
    if (!exists) {
      return res.status(404).json({
        error: 'Bundle not found',
        message: `Bundle with ID ${bundleId} does not exist or has expired`
      });
    }

    // Get bundle path and info
    const bundlePath = await bundleGenerator.getBundlePath(bundleId);
    const bundleSize = await bundleGenerator.getBundleSize(bundlePath);
    const fileName = `mcp-server-${bundleId}.zip`;

    // Set response headers for file download
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('X-Bundle-ID', bundleId);
    res.setHeader('X-Bundle-Size', bundleSize);

    // Stream the file
    const fs = require('fs');
    const fileStream = fs.createReadStream(bundlePath);
    
    fileStream.on('error', (error: Error) => {
      console.error('File stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Download failed',
          message: 'Unable to stream the bundle file'
        });
      }
    });

    fileStream.pipe(res);

  } catch (error) {
    next(error);
  }
});

// Get bundle info without downloading
router.get('/:bundleId/info', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { bundleId } = req.params;
    
    const bundleGenerator = new BundleGenerator();
    
    // Check if bundle exists
    const exists = await bundleGenerator.bundleExists(bundleId);
    if (!exists) {
      return res.status(404).json({
        error: 'Bundle not found',
        message: `Bundle with ID ${bundleId} does not exist or has expired`
      });
    }

    const bundlePath = await bundleGenerator.getBundlePath(bundleId);
    const bundleSize = await bundleGenerator.getBundleSize(bundlePath);
    
    // Get file stats
    const fs = require('fs');
    const stats = fs.statSync(bundlePath);

    res.json({
      bundleId,
      fileName: `mcp-server-${bundleId}.zip`,
      size: bundleSize,
      sizeBytes: stats.size,
      created: stats.birthtime,
      modified: stats.mtime,
      downloadUrl: `/api/download/${bundleId}`,
      available: true
    });

  } catch (error) {
    next(error);
  }
});

// List all available bundles (admin endpoint)
router.get('/', async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Simple authentication check (in production, use proper auth)
    const authHeader = req.headers.authorization;
    if (!authHeader || authHeader !== 'Bearer admin-token') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Admin token required'
      });
    }

    const bundleGenerator = new BundleGenerator();
    const bundles = await bundleGenerator.listBundles();
    const storageUsage = await bundleGenerator.getStorageUsage();

    res.json({
      bundles: bundles.map(bundle => ({
        id: bundle.id,
        size: bundle.size,
        created: bundle.created,
        downloadUrl: `/api/download/${bundle.id}`,
        infoUrl: `/api/download/${bundle.id}/info`
      })),
      summary: {
        totalBundles: bundles.length,
        totalSize: storageUsage.totalSize,
        totalFiles: storageUsage.totalFiles
      }
    });

  } catch (error) {
    next(error);
  }
});

// Delete bundle (admin endpoint)
router.delete('/:bundleId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Simple authentication check (in production, use proper auth)
    const authHeader = req.headers.authorization;
    if (!authHeader || authHeader !== 'Bearer admin-token') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Admin token required'
      });
    }

    const { bundleId } = req.params;
    const bundleGenerator = new BundleGenerator();
    
    const deleted = await bundleGenerator.deleteBundle(bundleId);
    
    if (deleted) {
      res.json({
        success: true,
        message: `Bundle ${bundleId} deleted successfully`
      });
    } else {
      res.status(404).json({
        success: false,
        error: 'Bundle not found',
        message: `Bundle ${bundleId} does not exist`
      });
    }

  } catch (error) {
    next(error);
  }
});

// Cleanup old bundles (admin endpoint)
router.post('/cleanup', async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Simple authentication check (in production, use proper auth)
    const authHeader = req.headers.authorization;
    if (!authHeader || authHeader !== 'Bearer admin-token') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Admin token required'
      });
    }

    const { maxAgeHours = 24 } = req.body;
    const bundleGenerator = new BundleGenerator();
    
    const deletedCount = await bundleGenerator.cleanupOldBundles(maxAgeHours);

    res.json({
      success: true,
      deletedCount,
      message: `Cleaned up ${deletedCount} old bundles (older than ${maxAgeHours} hours)`
    });

  } catch (error) {
    next(error);
  }
});

// Stream bundle creation and download (for large bundles)
router.post('/stream/:bundleId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { bundleId } = req.params;
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        error: 'Missing content',
        message: 'Bundle content is required'
      });
    }

    const bundleGenerator = new BundleGenerator();
    const fileName = `mcp-server-${bundleId}.zip`;

    // Set response headers for streaming download
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('X-Bundle-ID', bundleId);

    // Create and stream bundle directly
    const bundleStream = await bundleGenerator.createBundleStream(content);
    bundleStream.pipe(res);

    bundleStream.on('error', (error: Error) => {
      console.error('Bundle stream error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          error: 'Stream failed',
          message: 'Unable to create bundle stream'
        });
      }
    });

  } catch (error) {
    next(error);
  }
});

export { router as downloadRoutes };
export default router;
