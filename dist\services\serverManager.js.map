{"version": 3, "file": "serverManager.js", "sourceRoot": "", "sources": ["../../src/services/serverManager.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,6CAA+B;AAC/B,2CAA6B;AAC7B,iDAAoD;AACpD,mCAAsC;AAetC,MAAa,aAAc,SAAQ,qBAAY;IAO7C;QACE,KAAK,EAAE,CAAC;QAPF,YAAO,GAA+B,IAAI,GAAG,EAAE,CAAC;QAChD,cAAS,GAA8B,IAAI,GAAG,EAAE,CAAC;QACjD,aAAQ,GAAG,IAAI,CAAC;QAChB,eAAU,GAAG,EAAE,CAAC;QAChB,eAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,mBAAmB,CAAC,CAAC;QAIjE,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAC9B,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAChB,IAAY,EACZ,UAAkB,EAClB,OAAe,EACf,MAAY;QAEZ,MAAM,EAAE,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAEzC,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,8BAA8B,IAAI,CAAC,UAAU,WAAW,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,UAAU,GAAkB;YAChC,EAAE;YACF,IAAI;YACJ,IAAI;YACJ,MAAM,EAAE,UAAU;YAClB,OAAO;YACP,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAEjC,IAAI,CAAC;YACH,wBAAwB;YACxB,MAAM,IAAI,CAAC,mBAAmB,CAAC,EAAE,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;YAEtE,2BAA2B;YAC3B,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;YAElC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;YACvC,OAAO,UAAU,CAAC;QACpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC;YAC5B,UAAU,CAAC,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjF,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;YAC5C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,EAAU;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACvC,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC5B,CAAC;QAED,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,EAAU;QAC5B,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAC1B,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,iBAAiB;QAC1E,MAAM,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,EAAU;QAClB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY,CAAC,EAAU;QAC3B,MAAM,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAE1B,sBAAsB;QACtB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QACjD,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnC,MAAM,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QACxB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAC/B,EAAU,EACV,IAAY,EACZ,UAAkB,EAClB,OAAe,EACf,MAAY;QAEZ,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QACjD,MAAM,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE9B,iDAAiD;QACjD,MAAM,EAAE,aAAa,EAAE,GAAG,wDAAa,uBAAuB,GAAC,CAAC;QAChE,MAAM,EAAE,eAAe,EAAE,GAAG,wDAAa,yBAAyB,GAAC,CAAC;QAEpE,MAAM,MAAM,GAAG,IAAI,aAAa,EAAE,CAAC;QACnC,MAAM,SAAS,GAAG,IAAI,eAAe,EAAE,CAAC;QAExC,qBAAqB;QACrB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;QAErD,wBAAwB;QACxB,MAAM,YAAY,GAAG;YACnB,IAAI,EAAE,IAAI;YACV,OAAO,EAAE,OAAO;YAChB,WAAW,EAAE,4BAA4B,IAAI,EAAE;YAC/C,IAAI,EAAE,IAAI,CAAC,oBAAoB,EAAE;YACjC,OAAO,EAAE,OAAO;YAChB,GAAG,MAAM;SACV,CAAC;QAEF,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAEnE,sBAAsB;QACtB,KAAK,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAChD,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC3C,MAAM,EAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;QAED,mCAAmC;QACnC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC7C,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE,UAAU,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAAC,EAAU;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACpC,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAEvD,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;QAEjD,yBAAyB;QACzB,MAAM,YAAY,GAAG,IAAA,qBAAK,EAAC,KAAK,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;YAClD,GAAG,EAAE,SAAS;YACd,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;QAEH,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACpC,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;gBAChC,IAAI,IAAI,KAAK,CAAC;oBAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;;oBAC3B,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC,CAAC;YAC3D,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,aAAa,GAAG,IAAA,qBAAK,EAAC,KAAK,EAAE,CAAC,OAAO,CAAC,EAAE;YAC5C,GAAG,EAAE,SAAS;YACd,KAAK,EAAE,MAAM;YACb,GAAG,EAAE;gBACH,GAAG,OAAO,CAAC,GAAG;gBACd,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC5B,QAAQ,EAAE,MAAM,CAAC,OAAO;gBACxB,QAAQ,EAAE,YAAY;aACvB;SACF,CAAC,CAAC;QAEH,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACxC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACxC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YACjC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,8BAA8B,IAAI,EAAE,CAAC,CAAC;YACxD,MAAM,CAAC,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC;YACjD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YAClC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC;YACxB,MAAM,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC;YACpC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QACtC,MAAM,CAAC,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;QAC/B,MAAM,CAAC,MAAM,GAAG,SAAS,CAAC;QAE1B,qCAAqC;QACrC,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,IAAY;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;QAChE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,OAAO,GAAG,SAAS,IAAI,SAAS,EAAE,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QACrE,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC;QACzB,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,IAAI,EAAE,CAAC;QACT,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACtC,CAAC;IAED;;OAEG;IACK,wBAAwB;QAC9B,WAAW,CAAC,KAAK,IAAI,EAAE;YACrB,KAAK,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACxC,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;oBAChC,IAAI,CAAC;wBACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,oBAAoB,MAAM,CAAC,IAAI,SAAS,CAAC,CAAC;wBACvE,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;4BAChB,MAAM,CAAC,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;wBACtC,CAAC;6BAAM,CAAC;4BACN,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC;4BACxB,MAAM,CAAC,YAAY,GAAG,wBAAwB,QAAQ,CAAC,MAAM,EAAE,CAAC;wBAClE,CAAC;oBACH,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC;wBACxB,MAAM,CAAC,YAAY,GAAG,wBAAwB,KAAK,EAAE,CAAC;oBACxD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,yBAAyB;IACtC,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,OAAO;;;;;;;;;;;;qBAYU,CAAC;IACpB,CAAC;CACF;AAzSD,sCAySC;AAED,kBAAe,aAAa,CAAC"}