{"version": 3, "file": "mcpManifestGenerator.js", "sourceRoot": "", "sources": ["../../src/core/mcpManifestGenerator.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,oCASkB;AAClB,oCAA2D;AAE3D,MAAa,oBAAoB;IAC/B;;OAEG;IACH,aAAa,CAAC,MAAqB;QACjC,MAAM,KAAK,GAAc,EAAE,CAAC;QAE5B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACxC,MAAM,IAAI,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;YACnD,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACnB,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAqB,EAAE,MAAoB;QAC1D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YAEzC,MAAM,QAAQ,GAAgB;gBAC5B,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,WAAW,EAAE,MAAM,CAAC,WAAW;gBAC/B,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,KAAK;gBACL,MAAM,EAAE;oBACN,OAAO,EAAE,MAAM;oBACf,IAAI,EAAE,CAAC,gBAAgB,CAAC;oBACxB,GAAG,EAAE;wBACH,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE;wBAC5B,QAAQ,EAAE,MAAM,CAAC,OAAO;qBACzB;iBACF;aACF,CAAC;YAEF,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,0BAAkB,CAC1B,iCAAiC,EACjC,KAAc,CACf,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,QAAwB;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACjD,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAC3D,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAEzD,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,WAAW;YACX,WAAW;YACX,YAAY;SACb,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,QAAwB;QAC/C,wEAAwE;QACxE,IAAI,IAAI,GAAG,QAAQ,CAAC,WAAW,CAAC;QAEhC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,qCAAqC;YACrC,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI;iBAC5B,KAAK,CAAC,GAAG,CAAC;iBACV,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;iBAC7C,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAC,CAAC;YAElC,IAAI,GAAG,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAC5C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAC7C,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACb,CAAC;QAED,OAAO,IAAA,0BAAkB,EAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,uBAAuB,CAAC,QAAwB;QACtD,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;YACrB,OAAO,QAAQ,CAAC,OAAO,CAAC;QAC1B,CAAC;QAED,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,OAAO,QAAQ,CAAC,WAAW,CAAC;QAC9B,CAAC;QAED,4CAA4C;QAC5C,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAC7C,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAE3B,OAAO,GAAG,MAAM,IAAI,IAAI,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,QAAwB;QAClD,MAAM,UAAU,GAA+B,EAAE,CAAC;QAClD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAE9B,sBAAsB;QACtB,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,MAAM,CAAC,CAAC;QACpE,KAAK,MAAM,KAAK,IAAI,UAAU,EAAE,CAAC;YAC/B,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;gBACvB,GAAG,KAAK,CAAC,MAAM;gBACf,WAAW,EAAE,KAAK,CAAC,WAAW;aAC/B,CAAC;YACF,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,MAAM,WAAW,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,OAAO,CAAC,CAAC;QACtE,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,eAAe,GAA+B,EAAE,CAAC;YACvD,MAAM,aAAa,GAAa,EAAE,CAAC;YAEnC,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;gBAChC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;oBAC5B,GAAG,KAAK,CAAC,MAAM;oBACf,WAAW,EAAE,KAAK,CAAC,WAAW;iBAC/B,CAAC;gBACF,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACnB,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;YAED,UAAU,CAAC,KAAK,GAAG;gBACjB,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,eAAe;gBAC3B,QAAQ,EAAE,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;gBAC9D,WAAW,EAAE,kBAAkB;aAChC,CAAC;QACJ,CAAC;QAED,wBAAwB;QACxB,MAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;QACxE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5B,MAAM,gBAAgB,GAA+B,EAAE,CAAC;YACxD,MAAM,cAAc,GAAa,EAAE,CAAC;YAEpC,KAAK,MAAM,KAAK,IAAI,YAAY,EAAE,CAAC;gBACjC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;oBAC7B,GAAG,KAAK,CAAC,MAAM;oBACf,WAAW,EAAE,KAAK,CAAC,WAAW;iBAC/B,CAAC;gBACF,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;oBACnB,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;YAED,UAAU,CAAC,OAAO,GAAG;gBACnB,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,gBAAgB;gBAC5B,QAAQ,EAAE,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,SAAS;gBAChE,WAAW,EAAE,iBAAiB;aAC/B,CAAC;QACJ,CAAC;QAED,mBAAmB;QACnB,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YACzB,UAAU,CAAC,IAAI,GAAG;gBAChB,GAAG,QAAQ,CAAC,WAAW,CAAC,MAAM;gBAC9B,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,IAAI,cAAc;aAChE,CAAC;YAEF,IAAI,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC;gBAClC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACxB,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,EAAE;gBACd,WAAW,EAAE,8BAA8B;aAC5C,CAAC;QACJ,CAAC;QAED,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,UAAU;YACV,QAAQ,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YACpD,oBAAoB,EAAE,KAAK;SAC5B,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAwB;QACnD,8CAA8C;QAC9C,MAAM,eAAe,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAC7C,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,SAAS,CACxD,CAAC;QAEF,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,IAAI,eAAe,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAO;gBACL,GAAG,eAAe,CAAC,MAAM;gBACzB,WAAW,EAAE,eAAe,CAAC,WAAW;aACzC,CAAC;QACJ,CAAC;QAED,2DAA2D;QAC3D,IAAI,eAAe,CAAC,WAAW,EAAE,CAAC;YAChC,IAAI,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjD,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,eAAe,CAAC,WAAW;iBACzC,CAAC;YACJ,CAAC;iBAAM,IAAI,eAAe,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACxD,OAAO;oBACL,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,eAAe,CAAC,WAAW;iBACzC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,eAAe,CAAC,WAAW;SACzC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,QAAqB;QACpC,MAAM,MAAM,GAAa,EAAE,CAAC;QAE5B,wBAAwB;QACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC1B,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACtD,MAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;QAClD,CAAC;aAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACrB,MAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAC7B,MAAM,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,iBAAiB;QACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC/C,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9C,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO;YACL,KAAK,EAAE,MAAM,CAAC,MAAM,KAAK,CAAC;YAC1B,MAAM;SACP,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,IAAa,EAAE,KAAa;QAC/C,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,MAAM,MAAM,GAAG,QAAQ,KAAK,EAAE,CAAC;QAE/B,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,oBAAoB,CAAC,CAAC;QAC7C,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,2BAA2B,CAAC,CAAC;QACpD,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,6BAA6B,CAAC,CAAC;QACtD,CAAC;aAAM,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE,CAAC;YAChD,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,kCAAkC,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,KAAgB;QAM3B,MAAM,KAAK,GAAG;YACZ,KAAK,EAAE,KAAK,CAAC,MAAM;YACnB,UAAU,EAAE,CAAC;YACb,iBAAiB,EAAE,CAAC;YACpB,sBAAsB,EAAE,CAAC;SAC1B,CAAC;QAEF,IAAI,oBAAoB,GAAG,CAAC,CAAC;QAE7B,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,KAAK,CAAC,UAAU,EAAE,CAAC;YACrB,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtE,KAAK,CAAC,iBAAiB,EAAE,CAAC;YAC5B,CAAC;YAED,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC;gBAChC,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC;YAC1E,CAAC;QACH,CAAC;QAED,KAAK,CAAC,sBAAsB,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC;YAC7C,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,oBAAoB,GAAG,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG;YAC/D,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAqB;QACnC,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEpD,OAAO;;UAED,QAAQ,CAAC,IAAI;aACV,QAAQ,CAAC,OAAO;WAClB,SAAS,CAAC,KAAK;+BACK,SAAS,CAAC,UAAU;+BACpB,SAAS,CAAC,iBAAiB;uCACnB,SAAS,CAAC,sBAAsB;KAClE,CAAC,IAAI,EAAE,CAAC;IACX,CAAC;CACF;AAhXD,oDAgXC;AAED,iCAAiC;AACjC,kBAAe,oBAAoB,CAAC"}