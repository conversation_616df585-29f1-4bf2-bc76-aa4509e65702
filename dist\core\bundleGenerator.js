"use strict";
/**
 * Bundle generator for creating downloadable ZIP files
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BundleGenerator = void 0;
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const archiver_1 = __importDefault(require("archiver"));
const uuid_1 = require("uuid");
const types_1 = require("../types");
const utils_1 = require("../utils");
class BundleGenerator {
    constructor() {
        this.bundleDir = '/tmp/mcp-bundles';
        // Ensure bundle directory exists
        this.ensureBundleDir();
    }
    /**
     * Create a ZIP bundle with all generated files
     */
    async createBundle(bundleId, content) {
        try {
            const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
            const tempDir = path.join(this.bundleDir, bundleId);
            // Create temporary directory for files
            await (0, utils_1.ensureDir)(tempDir);
            // Write all files to temporary directory
            await this.writeFilesToTemp(tempDir, content);
            // Create ZIP archive
            await this.createZipArchive(tempDir, bundlePath);
            // Clean up temporary directory
            await fs.remove(tempDir);
            return bundlePath;
        }
        catch (error) {
            throw new types_1.ServerGenerationError(`Failed to create bundle: ${bundleId}`, error);
        }
    }
    /**
     * Write all generated files to temporary directory
     */
    async writeFilesToTemp(tempDir, content) {
        const { manifest, files } = content;
        // Write MCP manifest
        await fs.writeFile(path.join(tempDir, 'mcp.json'), JSON.stringify(manifest, null, 2));
        // Write all generated files
        for (const [filePath, fileContent] of Object.entries(files)) {
            const fullPath = path.join(tempDir, filePath);
            // Ensure directory exists
            await (0, utils_1.ensureDir)(path.dirname(fullPath));
            // Write file
            await fs.writeFile(fullPath, fileContent);
        }
        // Write additional metadata
        await this.writeMetadata(tempDir, content);
    }
    /**
     * Write bundle metadata
     */
    async writeMetadata(tempDir, content) {
        const metadata = {
            generatedAt: new Date().toISOString(),
            generator: 'openapi-to-mcp',
            version: '1.0.0',
            config: content.config,
            files: Object.keys(content.files),
            toolCount: content.manifest.tools.length
        };
        await fs.writeFile(path.join(tempDir, '.metadata.json'), JSON.stringify(metadata, null, 2));
        // Create installation instructions
        const instructions = this.generateInstallationInstructions(content.config);
        await fs.writeFile(path.join(tempDir, 'INSTALLATION.md'), instructions);
    }
    /**
     * Generate installation instructions
     */
    generateInstallationInstructions(config) {
        return `# Installation Instructions

## Quick Start

1. **Extract the bundle**:
   \`\`\`bash
   unzip ${config.name}.zip
   cd ${config.name}
   \`\`\`

2. **Install dependencies**:
   \`\`\`bash
   npm install
   \`\`\`

3. **Build the project**:
   \`\`\`bash
   npm run build
   \`\`\`

4. **Configure environment** (optional):
   \`\`\`bash
   cp .env.example .env
   # Edit .env file with your settings
   \`\`\`

5. **Start the server**:
   \`\`\`bash
   npm start
   \`\`\`

## Configuration

- **Port**: ${config.port} (configurable via PORT environment variable)
- **Base URL**: ${config.baseUrl}
- **Name**: ${config.name}
- **Version**: ${config.version}

## Testing

Once the server is running, test the health endpoint:
\`\`\`bash
curl http://localhost:${config.port}/health
\`\`\`

## MCP Protocol

Send requests to the MCP endpoint:
\`\`\`bash
curl -X POST http://localhost:${config.port}/mcp \\
  -H "Content-Type: application/json" \\
  -d '{
    "tool": "toolName",
    "parameters": { "param1": "value1" }
  }'
\`\`\`

## Generated Files

- \`src/server.ts\` - Main server application
- \`src/routes.ts\` - API route handlers
- \`src/types.ts\` - TypeScript type definitions
- \`mcp.json\` - MCP manifest file
- \`package.json\` - Node.js dependencies
- \`tsconfig.json\` - TypeScript configuration

For more information, see README.md.
`;
    }
    /**
     * Create ZIP archive from directory
     */
    async createZipArchive(sourceDir, outputPath) {
        return new Promise((resolve, reject) => {
            const output = fs.createWriteStream(outputPath);
            const archive = (0, archiver_1.default)('zip', {
                zlib: { level: 9 } // Maximum compression
            });
            output.on('close', () => {
                resolve();
            });
            archive.on('error', (error) => {
                reject(error);
            });
            archive.pipe(output);
            archive.directory(sourceDir, false);
            archive.finalize();
        });
    }
    /**
     * Check if bundle exists
     */
    async bundleExists(bundleId) {
        const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
        try {
            await fs.access(bundlePath);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * Get bundle file path
     */
    async getBundlePath(bundleId) {
        const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
        if (!(await this.bundleExists(bundleId))) {
            throw new Error(`Bundle ${bundleId} not found`);
        }
        return bundlePath;
    }
    /**
     * Get bundle file size
     */
    async getBundleSize(bundlePath) {
        try {
            const stats = await fs.stat(bundlePath);
            return (0, utils_1.formatBytes)(stats.size);
        }
        catch {
            return 'Unknown';
        }
    }
    /**
     * Delete bundle file
     */
    async deleteBundle(bundleId) {
        try {
            const bundlePath = path.join(this.bundleDir, `${bundleId}.zip`);
            await fs.remove(bundlePath);
            return true;
        }
        catch {
            return false;
        }
    }
    /**
     * List all bundles
     */
    async listBundles() {
        try {
            const files = await fs.readdir(this.bundleDir);
            const bundles = [];
            for (const file of files) {
                if (file.endsWith('.zip')) {
                    const bundleId = path.basename(file, '.zip');
                    const bundlePath = path.join(this.bundleDir, file);
                    const stats = await fs.stat(bundlePath);
                    bundles.push({
                        id: bundleId,
                        path: bundlePath,
                        size: (0, utils_1.formatBytes)(stats.size),
                        created: stats.birthtime
                    });
                }
            }
            return bundles.sort((a, b) => b.created.getTime() - a.created.getTime());
        }
        catch {
            return [];
        }
    }
    /**
     * Clean up old bundles (older than specified hours)
     */
    async cleanupOldBundles(maxAgeHours = 24) {
        try {
            const bundles = await this.listBundles();
            const cutoffTime = new Date(Date.now() - maxAgeHours * 60 * 60 * 1000);
            let deletedCount = 0;
            for (const bundle of bundles) {
                if (bundle.created < cutoffTime) {
                    if (await this.deleteBundle(bundle.id)) {
                        deletedCount++;
                    }
                }
            }
            return deletedCount;
        }
        catch {
            return 0;
        }
    }
    /**
     * Get total storage usage
     */
    async getStorageUsage() {
        try {
            const bundles = await this.listBundles();
            let totalBytes = 0;
            for (const bundle of bundles) {
                const stats = await fs.stat(bundle.path);
                totalBytes += stats.size;
            }
            return {
                totalFiles: bundles.length,
                totalSize: (0, utils_1.formatBytes)(totalBytes)
            };
        }
        catch {
            return {
                totalFiles: 0,
                totalSize: '0 Bytes'
            };
        }
    }
    /**
     * Ensure bundle directory exists
     */
    async ensureBundleDir() {
        try {
            await (0, utils_1.ensureDir)(this.bundleDir);
        }
        catch (error) {
            console.warn('Could not create bundle directory:', error);
            // Fallback to current directory
            this.bundleDir = path.join(process.cwd(), 'tmp', 'mcp-bundles');
            await (0, utils_1.ensureDir)(this.bundleDir);
        }
    }
    /**
     * Create bundle stream for direct download without saving to disk
     */
    async createBundleStream(content) {
        const tempDir = path.join(this.bundleDir, `temp-${(0, uuid_1.v4)()}`);
        try {
            // Create temporary files
            await this.writeFilesToTemp(tempDir, content);
            // Create archive stream
            const archive = (0, archiver_1.default)('zip', { zlib: { level: 9 } });
            // Add all files to archive
            archive.directory(tempDir, false);
            archive.finalize();
            // Clean up temp directory after stream ends
            archive.on('end', async () => {
                try {
                    await fs.remove(tempDir);
                }
                catch (error) {
                    console.warn('Failed to clean up temp directory:', tempDir, error);
                }
            });
            return archive;
        }
        catch (error) {
            // Clean up on error
            try {
                await fs.remove(tempDir);
            }
            catch { }
            throw error;
        }
    }
}
exports.BundleGenerator = BundleGenerator;
exports.default = BundleGenerator;
//# sourceMappingURL=bundleGenerator.js.map