/**
 * MCP Server Manager - Handles multiple running MCP servers
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import { spawn, ChildProcess } from 'child_process';
import { EventEmitter } from 'events';

export interface MCPServerInfo {
  id: string;
  name: string;
  port: number;
  status: 'starting' | 'running' | 'stopped' | 'error';
  pid?: number;
  baseUrl: string;
  openApiUrl?: string;
  createdAt: Date;
  lastHealthCheck?: Date;
  errorMessage?: string;
}

export class ServerManager extends EventEmitter {
  private servers: Map<string, MCPServerInfo> = new Map();
  private processes: Map<string, ChildProcess> = new Map();
  private basePort = 8000;
  private maxServers = 10;
  private serversDir = path.join(process.cwd(), 'generated-servers');

  constructor() {
    super();
    this.ensureServersDirectory();
    this.startHealthCheckInterval();
  }

  /**
   * Create and start a new MCP server
   */
  async createServer(
    name: string, 
    openApiUrl: string, 
    baseUrl: string,
    config?: any
  ): Promise<MCPServerInfo> {
    const id = this.generateServerId(name);
    const port = this.getNextAvailablePort();

    if (this.servers.size >= this.maxServers) {
      throw new Error(`Maximum number of servers (${this.maxServers}) reached`);
    }

    const serverInfo: MCPServerInfo = {
      id,
      name,
      port,
      status: 'starting',
      baseUrl,
      openApiUrl,
      createdAt: new Date()
    };

    this.servers.set(id, serverInfo);

    try {
      // Generate server files
      await this.generateServerFiles(id, name, openApiUrl, baseUrl, config);
      
      // Start the server process
      await this.startServerProcess(id);
      
      this.emit('serverCreated', serverInfo);
      return serverInfo;
    } catch (error) {
      serverInfo.status = 'error';
      serverInfo.errorMessage = error instanceof Error ? error.message : String(error);
      this.emit('serverError', serverInfo, error);
      throw error;
    }
  }

  /**
   * Stop a running MCP server
   */
  async stopServer(id: string): Promise<void> {
    const server = this.servers.get(id);
    if (!server) {
      throw new Error(`Server ${id} not found`);
    }

    const process = this.processes.get(id);
    if (process) {
      process.kill('SIGTERM');
      this.processes.delete(id);
    }

    server.status = 'stopped';
    this.emit('serverStopped', server);
  }

  /**
   * Restart a MCP server
   */
  async restartServer(id: string): Promise<void> {
    await this.stopServer(id);
    await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
    await this.startServerProcess(id);
  }

  /**
   * Get all servers
   */
  getAllServers(): MCPServerInfo[] {
    return Array.from(this.servers.values());
  }

  /**
   * Get server by ID
   */
  getServer(id: string): MCPServerInfo | undefined {
    return this.servers.get(id);
  }

  /**
   * Delete a server completely
   */
  async deleteServer(id: string): Promise<void> {
    await this.stopServer(id);
    
    // Remove server files
    const serverDir = path.join(this.serversDir, id);
    if (await fs.pathExists(serverDir)) {
      await fs.remove(serverDir);
    }

    this.servers.delete(id);
    this.emit('serverDeleted', id);
  }

  /**
   * Generate server files using the OpenAPI generator
   */
  private async generateServerFiles(
    id: string, 
    name: string, 
    openApiUrl: string, 
    baseUrl: string,
    config?: any
  ): Promise<void> {
    const serverDir = path.join(this.serversDir, id);
    await fs.ensureDir(serverDir);

    // Import the generator (assuming it's available)
    const { OpenAPIParser } = await import('../core/openapiParser');
    const { ServerGenerator } = await import('../core/serverGenerator');

    const parser = new OpenAPIParser();
    const generator = new ServerGenerator();

    // Parse OpenAPI spec
    const parsed = await parser.parseFromURL(openApiUrl);

    // Generate server files
    const serverConfig = {
      name: name,
      version: '1.0.0',
      description: `Generated MCP server for ${name}`,
      port: this.getNextAvailablePort(),
      baseUrl: baseUrl,
      ...config
    };

    const files = await generator.generateServer(parsed, serverConfig);

    // Write files to disk
    for (const [filePath, content] of Object.entries(files)) {
      const fullPath = path.join(serverDir, filePath);
      await fs.ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content);
    }

    // Create Dockerfile for the server
    const dockerfile = this.generateDockerfile();
    await fs.writeFile(path.join(serverDir, 'Dockerfile'), dockerfile);
  }

  /**
   * Start server process
   */
  private async startServerProcess(id: string): Promise<void> {
    const server = this.servers.get(id);
    if (!server) throw new Error(`Server ${id} not found`);

    const serverDir = path.join(this.serversDir, id);
    
    // Build the server first
    const buildProcess = spawn('npm', ['run', 'build'], {
      cwd: serverDir,
      stdio: 'pipe'
    });

    await new Promise((resolve, reject) => {
      buildProcess.on('close', (code) => {
        if (code === 0) resolve(void 0);
        else reject(new Error(`Build failed with code ${code}`));
      });
    });

    // Start the server
    const serverProcess = spawn('npm', ['start'], {
      cwd: serverDir,
      stdio: 'pipe',
      env: {
        ...process.env,
        PORT: server.port.toString(),
        BASE_URL: server.baseUrl,
        NODE_ENV: 'production'
      }
    });

    serverProcess.stdout?.on('data', (data) => {
      console.log(`[${id}] ${data.toString()}`);
    });

    serverProcess.stderr?.on('data', (data) => {
      console.error(`[${id}] ${data.toString()}`);
    });

    serverProcess.on('close', (code) => {
      console.log(`[${id}] Process exited with code ${code}`);
      server.status = code === 0 ? 'stopped' : 'error';
      this.processes.delete(id);
      this.emit('serverStopped', server);
    });

    serverProcess.on('error', (error) => {
      console.error(`[${id}] Process error:`, error);
      server.status = 'error';
      server.errorMessage = error.message;
      this.emit('serverError', server, error);
    });

    this.processes.set(id, serverProcess);
    server.pid = serverProcess.pid;
    server.status = 'running';

    // Wait a bit for the server to start
    await new Promise(resolve => setTimeout(resolve, 3000));
  }

  /**
   * Generate unique server ID
   */
  private generateServerId(name: string): string {
    const sanitized = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const timestamp = Date.now();
    return `${sanitized}-${timestamp}`;
  }

  /**
   * Get next available port
   */
  private getNextAvailablePort(): number {
    const usedPorts = Array.from(this.servers.values()).map(s => s.port);
    let port = this.basePort;
    while (usedPorts.includes(port)) {
      port++;
    }
    return port;
  }

  /**
   * Ensure servers directory exists
   */
  private async ensureServersDirectory(): Promise<void> {
    await fs.ensureDir(this.serversDir);
  }

  /**
   * Start health check interval
   */
  private startHealthCheckInterval(): void {
    setInterval(async () => {
      for (const [id, server] of this.servers) {
        if (server.status === 'running') {
          try {
            const response = await fetch(`http://localhost:${server.port}/health`);
            if (response.ok) {
              server.lastHealthCheck = new Date();
            } else {
              server.status = 'error';
              server.errorMessage = `Health check failed: ${response.status}`;
            }
          } catch (error) {
            server.status = 'error';
            server.errorMessage = `Health check failed: ${error}`;
          }
        }
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Generate Dockerfile for individual MCP server
   */
  private generateDockerfile(): string {
    return `FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 8000

CMD ["npm", "start"]`;
  }
}

export default ServerManager;
