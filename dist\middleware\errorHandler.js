"use strict";
/**
 * Error handling middleware
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandler = errorHandler;
const types_1 = require("../types");
function errorHandler(error, req, res, next) {
    // Log error for debugging
    console.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        url: req.originalUrl,
        method: req.method,
        timestamp: new Date().toISOString()
    });
    // Handle specific error types
    if (error instanceof types_1.OpenAPIParseError) {
        res.status(400).json({
            error: 'OpenAPI Parse Error',
            message: error.message,
            type: 'validation_error',
            timestamp: new Date().toISOString()
        });
        return;
    }
    if (error instanceof types_1.MCPGenerationError) {
        res.status(422).json({
            error: 'MCP Generation Error',
            message: error.message,
            type: 'generation_error',
            timestamp: new Date().toISOString()
        });
        return;
    }
    if (error instanceof types_1.ServerGenerationError) {
        res.status(422).json({
            error: 'Server Generation Error',
            message: error.message,
            type: 'generation_error',
            timestamp: new Date().toISOString()
        });
        return;
    }
    // Handle validation errors
    if (error.name === 'ValidationError') {
        res.status(400).json({
            error: 'Validation Error',
            message: error.message,
            type: 'validation_error',
            timestamp: new Date().toISOString()
        });
        return;
    }
    // Handle JSON parse errors
    if (error instanceof SyntaxError && 'body' in error) {
        res.status(400).json({
            error: 'Invalid JSON',
            message: 'Request body contains invalid JSON',
            type: 'parse_error',
            timestamp: new Date().toISOString()
        });
        return;
    }
    // Handle file size errors
    if (error.message.includes('limit')) {
        res.status(413).json({
            error: 'Payload Too Large',
            message: 'Request body exceeds size limit',
            type: 'size_error',
            timestamp: new Date().toISOString()
        });
        return;
    }
    // Handle timeout errors
    if (error.message.includes('timeout')) {
        res.status(408).json({
            error: 'Request Timeout',
            message: 'Request took too long to process',
            type: 'timeout_error',
            timestamp: new Date().toISOString()
        });
        return;
    }
    // Default error response
    const isDevelopment = process.env.NODE_ENV === 'development';
    res.status(500).json({
        error: 'Internal Server Error',
        message: isDevelopment ? error.message : 'An unexpected error occurred',
        type: 'server_error',
        timestamp: new Date().toISOString(),
        ...(isDevelopment && { stack: error.stack })
    });
}
exports.default = errorHandler;
//# sourceMappingURL=errorHandler.js.map