{"ast": null, "code": "var _jsxFileName = \"D:\\\\repos-personal\\\\repos\\\\openapi-to-mcp\\\\ui\\\\src\\\\pages\\\\ChatPage.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '../components/ui/Button';\nimport { useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Helper to get query param\nfunction useQuery() {\n  _s();\n  return new URLSearchParams(useLocation().search);\n}\n_s(useQuery, \"EuD9q2dZ34PfN/QO2OBhBzeMxmY=\", false, function () {\n  return [useLocation];\n});\nexport const ChatPage = () => {\n  _s2();\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState('');\n  const [loading, setLoading] = useState(false);\n  const chatEndRef = useRef(null);\n  const query = useQuery();\n  // Get MCP server URL from query param, fallback to localhost:3000\n  const mcpServerUrl = query.get('mcpServerUrl') || 'http://localhost:3000';\n\n  // Scroll to bottom on new message\n  useEffect(() => {\n    var _chatEndRef$current;\n    (_chatEndRef$current = chatEndRef.current) === null || _chatEndRef$current === void 0 ? void 0 : _chatEndRef$current.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n  const sendMessage = async () => {\n    if (!input.trim()) return;\n    const userMsg = {\n      role: 'user',\n      content: input\n    };\n    setMessages(msgs => [...msgs, userMsg]);\n    setInput('');\n    setLoading(true);\n    try {\n      // Send to MCP server's /chat endpoint\n      const res = await fetch(`${mcpServerUrl}/chat`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          messages\n        }) // Send full history\n      });\n      const data = await res.json();\n      setMessages(msgs => [...msgs, {\n        role: 'assistant',\n        content: data.response || JSON.stringify(data)\n      }]);\n    } catch (err) {\n      setMessages(msgs => [...msgs, {\n        role: 'assistant',\n        content: 'Error: ' + (err.message || err.toString())\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleInputKeyDown = e => {\n    if (e.key === 'Enter' && !loading) {\n      sendMessage();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-2xl mx-auto p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      className: \"text-2xl font-bold mb-4\",\n      children: \"Chat with MCP Server\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded shadow p-4 h-96 overflow-y-auto flex flex-col mb-4\",\n      children: [messages.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-gray-400 text-center my-auto\",\n        children: \"Start chatting with your API...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 11\n      }, this), messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `mb-2 flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `px-3 py-2 rounded-lg max-w-xs ${msg.role === 'user' ? 'bg-indigo-600 text-white' : 'bg-gray-100 text-gray-900'}`,\n          children: msg.content\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)\n      }, idx, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: chatEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex gap-2\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        className: \"flex-1 border rounded px-3 py-2\",\n        placeholder: \"Type your message...\",\n        value: input,\n        onChange: e => setInput(e.target.value),\n        onKeyDown: handleInputKeyDown,\n        disabled: loading\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: sendMessage,\n        disabled: loading || !input.trim(),\n        children: \"Send\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-xs text-gray-400 mt-2\",\n      children: [\"(Chatting with MCP server at \", /*#__PURE__*/_jsxDEV(\"code\", {\n        children: [mcpServerUrl, \"/chat\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 38\n      }, this), \")\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_s2(ChatPage, \"BsrT7DYL1peQM0r97riv+bdxUCE=\", false, function () {\n  return [useQuery];\n});\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON>", "useLocation", "jsxDEV", "_jsxDEV", "useQuery", "_s", "URLSearchParams", "search", "ChatPage", "_s2", "messages", "setMessages", "input", "setInput", "loading", "setLoading", "chatEndRef", "query", "mcpServerUrl", "get", "_chatEndRef$current", "current", "scrollIntoView", "behavior", "sendMessage", "trim", "userMsg", "role", "content", "msgs", "res", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "json", "response", "err", "message", "toString", "handleInputKeyDown", "e", "key", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "map", "msg", "idx", "ref", "type", "placeholder", "value", "onChange", "target", "onKeyDown", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/pages/ChatPage.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport { Button } from '../components/ui/Button';\r\nimport { useLocation } from 'react-router-dom';\r\n\r\ninterface ChatMessage {\r\n  role: 'user' | 'assistant' | 'server';\r\n  content: string;\r\n}\r\n\r\n// Helper to get query param\r\nfunction useQuery() {\r\n  return new URLSearchParams(useLocation().search);\r\n}\r\n\r\nexport const ChatPage: React.FC = () => {\r\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\r\n  const [input, setInput] = useState('');\r\n  const [loading, setLoading] = useState(false);\r\n  const chatEndRef = useRef<HTMLDivElement>(null);\r\n\r\n  const query = useQuery();\r\n  // Get MCP server URL from query param, fallback to localhost:3000\r\n  const mcpServerUrl = query.get('mcpServerUrl') || 'http://localhost:3000';\r\n\r\n  // Scroll to bottom on new message\r\n  useEffect(() => {\r\n    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [messages]);\r\n\r\n  const sendMessage = async () => {\r\n    if (!input.trim()) return;\r\n    const userMsg: ChatMessage = { role: 'user', content: input };\r\n    setMessages((msgs) => [...msgs, userMsg]);\r\n    setInput('');\r\n    setLoading(true);\r\n\r\n    try {\r\n      // Send to MCP server's /chat endpoint\r\n      const res = await fetch(`${mcpServerUrl}/chat`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ messages }), // Send full history\r\n      });\r\n      const data = await res.json();\r\n      setMessages((msgs) => [\r\n        ...msgs,\r\n        { role: 'assistant', content: data.response || JSON.stringify(data) },\r\n      ]);\r\n    } catch (err: any) {\r\n      setMessages((msgs) => [\r\n        ...msgs,\r\n        { role: 'assistant', content: 'Error: ' + (err.message || err.toString()) },\r\n      ]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === 'Enter' && !loading) {\r\n      sendMessage();\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-2xl mx-auto p-6\">\r\n      <h1 className=\"text-2xl font-bold mb-4\">Chat with MCP Server</h1>\r\n      <div className=\"bg-white rounded shadow p-4 h-96 overflow-y-auto flex flex-col mb-4\">\r\n        {messages.length === 0 && (\r\n          <div className=\"text-gray-400 text-center my-auto\">Start chatting with your API...</div>\r\n        )}\r\n        {messages.map((msg, idx) => (\r\n          <div\r\n            key={idx}\r\n            className={`mb-2 flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}\r\n          >\r\n            <div\r\n              className={`px-3 py-2 rounded-lg max-w-xs ${\r\n                msg.role === 'user'\r\n                  ? 'bg-indigo-600 text-white'\r\n                  : 'bg-gray-100 text-gray-900'\r\n              }`}\r\n            >\r\n              {msg.content}\r\n            </div>\r\n          </div>\r\n        ))}\r\n        <div ref={chatEndRef} />\r\n      </div>\r\n      <div className=\"flex gap-2\">\r\n        <input\r\n          type=\"text\"\r\n          className=\"flex-1 border rounded px-3 py-2\"\r\n          placeholder=\"Type your message...\"\r\n          value={input}\r\n          onChange={(e) => setInput(e.target.value)}\r\n          onKeyDown={handleInputKeyDown}\r\n          disabled={loading}\r\n        />\r\n        <Button onClick={sendMessage} disabled={loading || !input.trim()}>\r\n          Send\r\n        </Button>\r\n      </div>\r\n      <div className=\"text-xs text-gray-400 mt-2\">\r\n        (Chatting with MCP server at <code>{mcpServerUrl}/chat</code>)\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,QAAQ,yBAAyB;AAChD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAO/C;AACA,SAASC,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAClB,OAAO,IAAIC,eAAe,CAACL,WAAW,CAAC,CAAC,CAACM,MAAM,CAAC;AAClD;AAACF,EAAA,CAFQD,QAAQ;EAAA,QACYH,WAAW;AAAA;AAGxC,OAAO,MAAMO,QAAkB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAgB,EAAE,CAAC;EAC3D,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMmB,UAAU,GAAGlB,MAAM,CAAiB,IAAI,CAAC;EAE/C,MAAMmB,KAAK,GAAGb,QAAQ,CAAC,CAAC;EACxB;EACA,MAAMc,YAAY,GAAGD,KAAK,CAACE,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;EAEzE;EACApB,SAAS,CAAC,MAAM;IAAA,IAAAqB,mBAAA;IACd,CAAAA,mBAAA,GAAAJ,UAAU,CAACK,OAAO,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACb,QAAQ,CAAC,CAAC;EAEd,MAAMc,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI,CAACZ,KAAK,CAACa,IAAI,CAAC,CAAC,EAAE;IACnB,MAAMC,OAAoB,GAAG;MAAEC,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAEhB;IAAM,CAAC;IAC7DD,WAAW,CAAEkB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEH,OAAO,CAAC,CAAC;IACzCb,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACF;MACA,MAAMe,GAAG,GAAG,MAAMC,KAAK,CAAC,GAAGb,YAAY,OAAO,EAAE;QAC9Cc,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UAAE,cAAc,EAAE;QAAmB,CAAC;QAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAE1B;QAAS,CAAC,CAAC,CAAE;MACtC,CAAC,CAAC;MACF,MAAM2B,IAAI,GAAG,MAAMP,GAAG,CAACQ,IAAI,CAAC,CAAC;MAC7B3B,WAAW,CAAEkB,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEF,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAES,IAAI,CAACE,QAAQ,IAAIJ,IAAI,CAACC,SAAS,CAACC,IAAI;MAAE,CAAC,CACtE,CAAC;IACJ,CAAC,CAAC,OAAOG,GAAQ,EAAE;MACjB7B,WAAW,CAAEkB,IAAI,IAAK,CACpB,GAAGA,IAAI,EACP;QAAEF,IAAI,EAAE,WAAW;QAAEC,OAAO,EAAE,SAAS,IAAIY,GAAG,CAACC,OAAO,IAAID,GAAG,CAACE,QAAQ,CAAC,CAAC;MAAE,CAAC,CAC5E,CAAC;IACJ,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,kBAAkB,GAAIC,CAAwC,IAAK;IACvE,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAAC/B,OAAO,EAAE;MACjCU,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,oBACErB,OAAA;IAAK2C,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBACpC5C,OAAA;MAAI2C,SAAS,EAAC,yBAAyB;MAAAC,QAAA,EAAC;IAAoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjEhD,OAAA;MAAK2C,SAAS,EAAC,qEAAqE;MAAAC,QAAA,GACjFrC,QAAQ,CAAC0C,MAAM,KAAK,CAAC,iBACpBjD,OAAA;QAAK2C,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAA+B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACxF,EACAzC,QAAQ,CAAC2C,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBACrBpD,OAAA;QAEE2C,SAAS,EAAE,aAAaQ,GAAG,CAAC3B,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,eAAe,EAAG;QAAAoB,QAAA,eAEhF5C,OAAA;UACE2C,SAAS,EAAE,iCACTQ,GAAG,CAAC3B,IAAI,KAAK,MAAM,GACf,0BAA0B,GAC1B,2BAA2B,EAC9B;UAAAoB,QAAA,EAEFO,GAAG,CAAC1B;QAAO;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT;MAAC,GAXDI,GAAG;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYL,CACN,CAAC,eACFhD,OAAA;QAAKqD,GAAG,EAAExC;MAAW;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eACNhD,OAAA;MAAK2C,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB5C,OAAA;QACEsD,IAAI,EAAC,MAAM;QACXX,SAAS,EAAC,iCAAiC;QAC3CY,WAAW,EAAC,sBAAsB;QAClCC,KAAK,EAAE/C,KAAM;QACbgD,QAAQ,EAAGhB,CAAC,IAAK/B,QAAQ,CAAC+B,CAAC,CAACiB,MAAM,CAACF,KAAK,CAAE;QAC1CG,SAAS,EAAEnB,kBAAmB;QAC9BoB,QAAQ,EAAEjD;MAAQ;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CAAC,eACFhD,OAAA,CAACH,MAAM;QAACgE,OAAO,EAAExC,WAAY;QAACuC,QAAQ,EAAEjD,OAAO,IAAI,CAACF,KAAK,CAACa,IAAI,CAAC,CAAE;QAAAsB,QAAA,EAAC;MAElE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eACNhD,OAAA;MAAK2C,SAAS,EAAC,4BAA4B;MAAAC,QAAA,GAAC,+BACb,eAAA5C,OAAA;QAAA4C,QAAA,GAAO7B,YAAY,EAAC,OAAK;MAAA;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,KAC/D;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,GAAA,CA9FWD,QAAkB;EAAA,QAMfJ,QAAQ;AAAA;AAAA6D,EAAA,GANXzD,QAAkB;AAAA,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}