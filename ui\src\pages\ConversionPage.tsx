/**
 * Conversion Page component
 * Generate MCP servers from OpenAPI specifications
 */

import React, { useState, useRef } from 'react';
import { Button } from '../components/ui/Button';

export const ConversionPage: React.FC = () => {
  const [isConverting, setIsConverting] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [openApiUrl, setOpenApiUrl] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [downloadUrl, setDownloadUrl] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    setSelectedFile(file);
    setOpenApiUrl('');
    setError(null);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    const file = files[0];
    if (file) {
      if (file.type === 'application/json' || file.name.endsWith('.json') || 
          file.name.endsWith('.yaml') || file.name.endsWith('.yml')) {
        handleFileSelect(file);
      } else {
        setError('Please select a JSON or YAML file');
      }
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setOpenApiUrl(e.target.value);
    setSelectedFile(null);
    setError(null);
  };

  // INSTANT MCP: Only support OpenAPI URL for now
  const handleInstantConvert = async () => {
    setIsConverting(true);
    setError(null);

    try {
      if (!openApiUrl.trim()) {
        setError('Please enter an OpenAPI URL');
        setIsConverting(false);
        return;
      }
      const res = await fetch('/api/instant-mcp', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ openapiUrl: openApiUrl.trim() }),
      });
      if (!res.ok) {
        const errJson = await res.json();
        setError(errJson.error || 'Failed to generate MCP server');
        setIsConverting(false);
        return;
      }
      const data = await res.json();
      // Instead of redirecting to chat, show success and download link
      setSuccess('MCP server generated successfully!');
      setDownloadUrl(data.downloadUrl || `/api/download/${data.bundleId}`);
    } catch (err: any) {
      setError(err.message || 'Conversion failed');
    } finally {
      setIsConverting(false);
    }
  };

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-6">Generate MCP Server</h1>
        <div className="bg-white shadow rounded-lg p-6">
          {/* URL input */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700">
              Paste OpenAPI URL
            </label>
            <div className="mt-1">
              <input
                type="url"
                value={openApiUrl}
                onChange={handleUrlChange}
                className="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                placeholder="https://api.example.com/openapi.json"
              />
            </div>
          </div>
          {/* Error message */}
          {error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}
          {/* Success message */}
          {success && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-700">{success}</p>
              {downloadUrl && (
                <a
                  href={downloadUrl}
                  className="mt-2 inline-block text-sm text-blue-600 hover:text-blue-800 underline"
                  download
                >
                  Download MCP Server
                </a>
              )}
            </div>
          )}
          {/* Convert button */}
          <div className="mt-6">
            <Button
              onClick={handleInstantConvert}
              disabled={!openApiUrl.trim()}
              loading={isConverting}
              className="w-full"
            >
              {isConverting ? 'Generating MCP Server...' : 'Generate MCP Server'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
