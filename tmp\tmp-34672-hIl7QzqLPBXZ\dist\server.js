"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const dotenv_1 = __importDefault(require("dotenv"));
const path = __importStar(require("path"));
const fs_1 = __importDefault(require("fs"));
// Try to load .env from both root and dist (for manual runs)
const envPathRoot = path.resolve(__dirname, '../.env');
const envPathDist = path.resolve(__dirname, '../../.env');
if (fs_1.default.existsSync(envPathRoot)) {
    dotenv_1.default.config({ path: envPathRoot });
}
else if (fs_1.default.existsSync(envPathDist)) {
    dotenv_1.default.config({ path: envPathDist });
}
else {
    dotenv_1.default.config();
}
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const axios_1 = __importDefault(require("axios"));
const routes_1 = require("./routes");
const app = (0, express_1.default)();
const PORT = process.env.PORT || 8000;
const BASE_URL = process.env.BASE_URL || 'https://petstore3.swagger.io/api/v3';
// Security middleware
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)());
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
// Health check
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        name: 'instant-mcp',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        baseUrl: BASE_URL
    });
});
// MCP tool endpoints
app.use('/tools', routes_1.router);
// Define tool name mapping
const toolNameToEndpoint = {
    'findPetsByStatus': 'findPets', // Map LLM tool name to actual endpoint
    // Add more mappings as needed
};

// MCP protocol handler
app.post('/mcp', async (req, res) => {
    try {
        const { tool, parameters } = req.body;
        
        // Map tool name to actual endpoint if needed
        const endpointName = toolNameToEndpoint[tool] || tool;
        
        // Create the full URL for better logging
        const toolUrl = `http://localhost:${PORT}/tools/${endpointName}`;
        
        console.log(`[MCP] Incoming request for tool: ${tool} (mapped to: ${endpointName})`);
        console.log(`[MCP] Tool URL: ${toolUrl}`);
        console.log(`[MCP] Parameters:`, JSON.stringify(parameters, null, 2));
        
        // Route to appropriate tool handler using the mapped name
        const response = await fetch(toolUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(parameters)
        });
        
        console.log(`[MCP] Response status: ${response.status}`);
        
        // Check if response is ok before parsing JSON
        if (!response.ok) {
            const errorText = await response.text();
            console.error(`[MCP] Tool endpoint error: ${response.status}`);
            console.error(`[MCP] Error details: ${errorText}`);
            return res.status(response.status).json({
                success: false,
                error: `Tool '${endpointName}' request failed with status ${response.status}`,
                details: errorText
            });
        }
        
        const result = await response.json();
        console.log(`[MCP] Tool response:`, JSON.stringify(result, null, 2));
        
        res.json({
            success: true,
            result
        });
    }
    catch (error) {
        console.error('[MCP] Error in MCP handler:', error);
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// LLM Chat endpoint with dynamic OpenAPI tool calling support
app.post('/chat', async (req, res) => {
    console.log('[MCP CHAT] Received chat request');
    
    // Accept either { message } or { messages } (chat history)
    let messages = req.body.messages;
    const singleMessage = req.body.message;
    
    console.log('[MCP CHAT] Message content:', singleMessage || (messages && messages.length > 0 ? messages[messages.length-1].content : 'No message'));
    
    const headers = {};
    if (process.env.LITELLM_API_KEY) {
        headers['Authorization'] = `Bearer ${process.env.LITELLM_API_KEY}`;
    }
    
    const llmUrl = process.env.LITELLM_URL || 'http://localhost:4000/chat/completions';
    const llmModel = process.env.LITELLM_MODEL || 'gpt-3.5-turbo';
    
    // Dynamically generate tools/functions from OpenAPI endpoints
    const openApiTools = require('./types').openApiTools || [];
    const tools = openApiTools;
    
    console.log('[MCP CHAT] Available tools:', tools.map(t => t.function.name).join(', '));
    
    // Enhanced system prompt for better tool calling
    const systemPrompt = `You are an API assistant for this service. Help users interact with the API using natural language.

IMPORTANT: Only use the following available tools/functions:
${openApiTools.map((tool) => `- ${tool.function.name}: ${tool.function.description}`).join('\n')}

Never invent or guess tool names. If a user asks for something that doesn't match these exact tools, explain what's available and ask for clarification.`;

    // Compose the OpenAI/LiteLLM request with tool calling
    let payloadMessages;
    if (Array.isArray(messages) && messages.length > 0) {
        // Use provided chat history
        payloadMessages = [{ role: 'system', content: systemPrompt }, ...messages];
    } else if (singleMessage) {
        // Fallback to single message
        payloadMessages = [{ role: 'system', content: systemPrompt }, { role: 'user', content: singleMessage }];
    } else {
        return res.status(400).json({ error: 'No message(s) provided' });
    }
    
    const payload = {
        model: llmModel,
        messages: payloadMessages,
        tools,
        tool_choice: "auto"
    };
    
    console.log('[MCP CHAT] Sending request to LLM:', {
        url: llmUrl,
        model: llmModel,
        apiKeyPresent: !!process.env.LITELLM_API_KEY,
        messageCount: payloadMessages.length,
        toolCount: tools.length
    });
    
    try {
        const llmRes = await axios_1.default.post(llmUrl, payload, { headers });
        console.log('[MCP CHAT] Received response from LLM');
        
        // Check if the LLM wants to call a function/tool
        const toolCall = llmRes.data?.choices?.[0]?.message?.tool_calls?.[0];
        console.log('[MCP CHAT] Tool call present:', !!toolCall);
        
        if (toolCall && toolCall.function) {
            const { name, arguments: argsJson } = toolCall.function;
            console.log(`[MCP CHAT] Tool call detected: ${name}`);
            
            let args = {};
            try {
                args = JSON.parse(argsJson);
                console.log(`[MCP CHAT] Parsed arguments:`, args);
            }
            catch (e) {
                console.error('[MCP CHAT] Failed to parse arguments:', e);
                args = {};
            }
            
            // DEBUG LOGGING: Show tool call details
            console.log('[MCP CHAT] Tool call:', { name, argsJson, args });
            
            // Call the corresponding tool endpoint directly via /mcp
            try {
                console.log('[MCP CHAT] Calling /mcp endpoint with tool:', name);
                const mcpPayload = {
                    tool: name,
                    parameters: args
                };
                
                // Log the full request details
                console.log(`[MCP CHAT] MCP request URL: http://localhost:${PORT}/mcp`);
                console.log(`[MCP CHAT] MCP request payload:`, JSON.stringify(mcpPayload, null, 2));
                
                const apiRes = await axios_1.default.post(`http://localhost:${PORT}/mcp`, mcpPayload);
                console.log('[MCP CHAT] MCP response status:', apiRes.status);
                console.log('[MCP CHAT] MCP response data:', JSON.stringify(apiRes.data, null, 2));
                
                // Return a more detailed response to the client
                return res.json({ 
                    response: `Tool ${name} called successfully. Result: ${JSON.stringify(apiRes.data.result)}`,
                    toolCall: {
                        name,
                        parameters: args,
                        result: apiRes.data.result
                    }
                });
            }
            catch (apiErr) {
                console.error('[MCP CHAT] MCP call failed:', apiErr.message);
                
                // Log detailed error information
                if (apiErr.response) {
                    console.error('[MCP CHAT] Response status:', apiErr.response.status);
                    console.error('[MCP CHAT] Response data:', JSON.stringify(apiErr.response.data, null, 2));
                } else if (apiErr.request) {
                    console.error('[MCP CHAT] No response received, request details:', apiErr.request);
                } else {
                    console.error('[MCP CHAT] Error details:', apiErr);
                }
                
                // Try direct API call as fallback
                try {
                    console.log(`[MCP CHAT] Trying direct API call for ${name}`);
                    
                    // For findPetsByStatus, try to call the actual Petstore API
                    if (name === 'findPetsByStatus') {
                        const status = args.query?.status || 'available';
                        const petStoreUrl = `${BASE_URL}/pet/findByStatus?status=${status}`;
                        console.log(`[MCP CHAT] Calling Petstore API: ${petStoreUrl}`);
                        
                        const petRes = await axios_1.default.get(petStoreUrl);
                        console.log('[MCP CHAT] Petstore response:', petRes.status);
                        
                        return res.json({ 
                            response: `Found ${petRes.data.length} pets with status "${status}". First few: ${JSON.stringify(petRes.data.slice(0, 3))}`,
                            note: "Used direct API call as fallback"
                        });
                    }
                    
                    return res.status(500).json({ 
                        error: 'Tool call failed and no fallback available', 
                        details: apiErr.message
                    });
                }
                catch (fallbackErr) {
                    console.error('[MCP CHAT] Fallback API call failed:', fallbackErr.message);
                    
                    return res.status(500).json({ 
                        error: 'Tool call failed', 
                        details: apiErr.message,
                        fallbackError: fallbackErr.message
                    });
                }
            }
        }
        
        // Otherwise, return the LLM's message as usual
        const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);
        console.log('[MCP CHAT] Returning LLM message:', llmMessage.substring(0, 100) + '...');
        
        res.json({ response: llmMessage });
    }
    catch (err) {
        console.error('[MCP CHAT] LLM call failed:', err);
        if (err.response) {
            console.error('[MCP CHAT] Response status:', err.response.status);
            console.error('[MCP CHAT] Response data:', err.response.data);
        }
        
        res.status(500).json({
            error: 'LLM call failed',
            details: err && (err.response?.data || err.message || err.toString())
        });
    }
}); 
// Error handling
app.use((error, req, res, next) => {
    console.error('Server error:', error);
    res.status(500).json({
        error: 'Internal server error',
        message: error instanceof Error ? error.message : String(error)
    });
});
// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Not found',
        message: `Route ${req.originalUrl} not found`
    });
});
// Start server
const server = app.listen(PORT, () => {
    console.log(`🚀 instant-mcp MCP Server running on port ${PORT}`);
    console.log(`📖 Health check: http://localhost:${PORT}/health`);
    console.log(`🔧 Base URL: ${BASE_URL}`);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received, shutting down gracefully');
    server.close(() => {
        console.log('Server closed');
        process.exit(0);
    });
});
exports.default = app;
//# sourceMappingURL=server.js.map



