# Corporate Proxy Configuration

This document explains how to configure the OpenAPI-to-MCP system to work behind corporate proxies.

## 🌐 Environment Variables

Set these environment variables to configure proxy support:

### HTTP Proxy
```bash
export HTTP_PROXY=http://proxy.company.com:8080
export http_proxy=http://proxy.company.com:8080
```

### HTTPS Proxy
```bash
export HTTPS_PROXY=http://proxy.company.com:8080
export https_proxy=http://proxy.company.com:8080
```

### No Proxy (Bypass List)
```bash
export NO_PROXY=localhost,127.0.0.1,.local,.company.com,*.internal
export no_proxy=localhost,127.0.0.1,.local,.company.com,*.internal
```

## 🔧 Configuration Examples

### Windows (Command Prompt)
```cmd
set HTTP_PROXY=http://proxy.company.com:8080
set HTTPS_PROXY=http://proxy.company.com:8080
set NO_PROXY=localhost,127.0.0.1,.local,.company.com
```

### Windows (PowerShell)
```powershell
$env:HTTP_PROXY="http://proxy.company.com:8080"
$env:HTTPS_PROXY="http://proxy.company.com:8080"
$env:NO_PROXY="localhost,127.0.0.1,.local,.company.com"
```

### Linux/macOS
```bash
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
export NO_PROXY=localhost,127.0.0.1,.local,.company.com
```

### Docker Environment
```dockerfile
ENV HTTP_PROXY=http://proxy.company.com:8080
ENV HTTPS_PROXY=http://proxy.company.com:8080
ENV NO_PROXY=localhost,127.0.0.1,.local,.company.com
```

## 📝 .env File Configuration

Create a `.env` file in your project root:

```env
# Proxy Configuration
HTTP_PROXY=http://proxy.company.com:8080
HTTPS_PROXY=http://proxy.company.com:8080
NO_PROXY=localhost,127.0.0.1,.local,.company.com

# Application Configuration
PORT=3000
NODE_ENV=development
LITELLM_URL=http://localhost:4000/chat/completions
LITELLM_MODEL=gpt-3.5-turbo
```

## 🔍 Proxy Authentication

If your proxy requires authentication:

```bash
export HTTP_PROXY=http://username:<EMAIL>:8080
export HTTPS_PROXY=http://username:<EMAIL>:8080
```

**Security Note**: Avoid putting credentials in plain text. Use environment variables or secure credential storage.

## 🎯 NO_PROXY Patterns

The `NO_PROXY` variable supports various patterns:

- **Exact match**: `localhost`
- **Domain suffix**: `.company.com` (matches any subdomain)
- **Wildcard**: `*.internal` (matches any subdomain)
- **IP addresses**: `127.0.0.1`, `***********/24`
- **Multiple entries**: Comma-separated list

### Examples:
```bash
# Basic bypass
NO_PROXY=localhost,127.0.0.1

# Company domains
NO_PROXY=localhost,127.0.0.1,.company.com,*.internal

# IP ranges (basic)
NO_PROXY=localhost,127.0.0.1,***********,10.0.0.0
```

## 🚀 Testing Proxy Configuration

### 1. Check Environment Variables
```bash
echo $HTTP_PROXY
echo $HTTPS_PROXY
echo $NO_PROXY
```

### 2. Test with curl
```bash
# Test HTTP proxy
curl -x $HTTP_PROXY http://httpbin.org/ip

# Test HTTPS proxy
curl -x $HTTPS_PROXY https://httpbin.org/ip

# Test bypass
curl --noproxy localhost http://localhost:3000/api/health
```

### 3. Application Logs
When you start the application, you'll see proxy configuration logs:
```
[PROXY] Configuration:
  HTTP_PROXY: http://proxy.company.com:8080
  HTTPS_PROXY: http://proxy.company.com:8080
  NO_PROXY: localhost,127.0.0.1,.company.com
```

## 🔧 Generated MCP Servers

Each generated MCP server automatically inherits proxy configuration:

1. **Environment variables** are passed through
2. **Proxy agents** are created for external API calls
3. **Bypass rules** are applied for local services

### MCP Server .env Template
Generated servers include proxy configuration in their `.env.example`:

```env
# Proxy Configuration (for corporate networks)
# HTTP_PROXY=http://proxy.company.com:8080
# HTTPS_PROXY=http://proxy.company.com:8080
# NO_PROXY=localhost,127.0.0.1,.local,.company.com
```

## 🐛 Troubleshooting

### Common Issues

1. **Connection timeouts**
   - Verify proxy URL and port
   - Check if proxy requires authentication
   - Ensure proxy is accessible from your network

2. **SSL/TLS errors**
   - Some proxies intercept HTTPS traffic
   - You may need to configure SSL certificates
   - Try using HTTP proxy for HTTPS traffic

3. **Authentication failures**
   - Verify username/password
   - Check if domain is required: `domain\\username`
   - Some proxies use NTLM authentication

### Debug Mode
Enable debug logging:
```bash
export DEBUG=proxy,axios
npm run dev
```

### Test Specific URLs
```bash
# Test OpenAPI spec fetching
curl -x $HTTPS_PROXY https://petstore3.swagger.io/api/v3/openapi.json

# Test LiteLLM gateway
curl -x $HTTP_PROXY http://localhost:4000/health
```

## 📚 Additional Resources

- [Node.js HTTP Proxy Documentation](https://nodejs.org/api/http.html#http_class_http_agent)
- [Axios Proxy Configuration](https://axios-http.com/docs/req_config)
- [Corporate Proxy Best Practices](https://docs.npmjs.com/cli/v7/using-npm/config#proxy)

## 🔒 Security Considerations

1. **Never commit proxy credentials** to version control
2. **Use environment variables** for sensitive configuration
3. **Rotate credentials** regularly
4. **Monitor proxy logs** for security events
5. **Use HTTPS proxies** when possible

## 💡 Tips

- Test proxy configuration with simple HTTP requests first
- Use `NO_PROXY` to bypass proxy for local development
- Consider using a proxy auto-configuration (PAC) file for complex setups
- Monitor network traffic to debug proxy issues

The system now automatically handles proxy configuration for all HTTP/HTTPS requests, making it compatible with corporate network environments! 🎉
