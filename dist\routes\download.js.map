{"version": 3, "file": "download.js", "sourceRoot": "", "sources": ["../../src/routes/download.ts"], "names": [], "mappings": ";AAAA;;GAEG;;;AAEH,qCAAkE;AAElE,6DAA0D;AAG1D,MAAM,MAAM,GAAG,IAAA,gBAAM,GAAE,CAAC;AA6OL,gCAAc;AA3OjC,wBAAwB;AACxB,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACjF,IAAI,CAAC;QACH,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEhC,mCAAmC;QACnC,MAAM,SAAS,GAAG,4EAA4E,CAAC;QAC/F,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,mBAAmB;gBAC1B,OAAO,EAAE,gCAAgC;aAC1C,CAAC,CAAC;QACL,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAE9C,yBAAyB;QACzB,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,kBAAkB;gBACzB,OAAO,EAAE,kBAAkB,QAAQ,gCAAgC;aACpE,CAAC,CAAC;QACL,CAAC;QAED,2BAA2B;QAC3B,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACnE,MAAM,QAAQ,GAAG,cAAc,QAAQ,MAAM,CAAC;QAE9C,yCAAyC;QACzC,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QACjD,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,yBAAyB,QAAQ,GAAG,CAAC,CAAC;QAC3E,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QACvC,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QAE3C,kBAAkB;QAClB,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,UAAU,GAAG,EAAE,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAEnD,UAAU,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACtC,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,iBAAiB;oBACxB,OAAO,EAAE,kCAAkC;iBAC5C,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAEvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,sCAAsC;AACtC,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACtF,IAAI,CAAC;QACH,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAEhC,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAE9C,yBAAyB;QACzB,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAC5D,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,kBAAkB;gBACzB,OAAO,EAAE,kBAAkB,QAAQ,gCAAgC;aACpE,CAAC,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjE,MAAM,UAAU,GAAG,MAAM,eAAe,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QAEnE,iBAAiB;QACjB,MAAM,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEtC,GAAG,CAAC,IAAI,CAAC;YACP,QAAQ;YACR,QAAQ,EAAE,cAAc,QAAQ,MAAM;YACtC,IAAI,EAAE,UAAU;YAChB,SAAS,EAAE,KAAK,CAAC,IAAI;YACrB,OAAO,EAAE,KAAK,CAAC,SAAS;YACxB,QAAQ,EAAE,KAAK,CAAC,KAAK;YACrB,WAAW,EAAE,iBAAiB,QAAQ,EAAE;YACxC,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,8CAA8C;AAC9C,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACxE,IAAI,CAAC;QACH,+DAA+D;QAC/D,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,oBAAoB,EAAE,CAAC;YACvD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,sBAAsB;aAChC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC9C,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,WAAW,EAAE,CAAC;QACpD,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,eAAe,EAAE,CAAC;QAE7D,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;gBAC9B,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,IAAI,EAAE,MAAM,CAAC,IAAI;gBACjB,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,WAAW,EAAE,iBAAiB,MAAM,CAAC,EAAE,EAAE;gBACzC,OAAO,EAAE,iBAAiB,MAAM,CAAC,EAAE,OAAO;aAC3C,CAAC,CAAC;YACH,OAAO,EAAE;gBACP,YAAY,EAAE,OAAO,CAAC,MAAM;gBAC5B,SAAS,EAAE,YAAY,CAAC,SAAS;gBACjC,UAAU,EAAE,YAAY,CAAC,UAAU;aACpC;SACF,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,iCAAiC;AACjC,MAAM,CAAC,MAAM,CAAC,YAAY,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACpF,IAAI,CAAC;QACH,+DAA+D;QAC/D,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,oBAAoB,EAAE,CAAC;YACvD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,sBAAsB;aAChC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAChC,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAE9C,MAAM,OAAO,GAAG,MAAM,eAAe,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAE7D,IAAI,OAAO,EAAE,CAAC;YACZ,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,UAAU,QAAQ,uBAAuB;aACnD,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,kBAAkB;gBACzB,OAAO,EAAE,UAAU,QAAQ,iBAAiB;aAC7C,CAAC,CAAC;QACL,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,uCAAuC;AACvC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IAChF,IAAI,CAAC;QACH,+DAA+D;QAC/D,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,UAAU,KAAK,oBAAoB,EAAE,CAAC;YACvD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,cAAc;gBACrB,OAAO,EAAE,sBAAsB;aAChC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QACtC,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAE9C,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAE1E,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,YAAY;YACZ,OAAO,EAAE,cAAc,YAAY,4BAA4B,WAAW,SAAS;SACpF,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,0DAA0D;AAC1D,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;IACzF,IAAI,CAAC;QACH,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAChC,MAAM,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;QAE7B,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,KAAK,EAAE,iBAAiB;gBACxB,OAAO,EAAE,4BAA4B;aACtC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,eAAe,GAAG,IAAI,iCAAe,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,cAAc,QAAQ,MAAM,CAAC;QAE9C,8CAA8C;QAC9C,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;QACjD,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,yBAAyB,QAAQ,GAAG,CAAC,CAAC;QAC3E,GAAG,CAAC,SAAS,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAEvC,oCAAoC;QACpC,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACvE,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEvB,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAY,EAAE,EAAE;YACxC,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,OAAO,EAAE,gCAAgC;iBAC1C,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC,CAAC;AAGH,kBAAe,MAAM,CAAC"}