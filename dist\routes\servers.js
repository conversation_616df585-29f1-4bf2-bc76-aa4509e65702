"use strict";
/**
 * Server management routes for multiple MCP servers
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.serversRoutes = void 0;
const express_1 = require("express");
const serverManager_1 = __importDefault(require("../services/serverManager"));
const router = (0, express_1.Router)();
exports.serversRoutes = router;
const serverManager = new serverManager_1.default();
// Get all servers
router.get('/', async (req, res) => {
    try {
        const servers = serverManager.getAllServers();
        res.json({
            success: true,
            servers,
            count: servers.length
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// Get specific server
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const server = serverManager.getServer(id);
        if (!server) {
            return res.status(404).json({
                success: false,
                error: 'Server not found'
            });
        }
        res.json({
            success: true,
            server
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// Create new server
router.post('/', async (req, res) => {
    try {
        const { name, openApiUrl, baseUrl, config } = req.body;
        if (!name || !openApiUrl || !baseUrl) {
            return res.status(400).json({
                success: false,
                error: 'Missing required fields: name, openApiUrl, baseUrl'
            });
        }
        const server = await serverManager.createServer(name, openApiUrl, baseUrl, config);
        res.status(201).json({
            success: true,
            server,
            message: 'Server created successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// Stop server
router.post('/:id/stop', async (req, res) => {
    try {
        const { id } = req.params;
        await serverManager.stopServer(id);
        res.json({
            success: true,
            message: 'Server stopped successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// Restart server
router.post('/:id/restart', async (req, res) => {
    try {
        const { id } = req.params;
        await serverManager.restartServer(id);
        res.json({
            success: true,
            message: 'Server restarted successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// Delete server
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await serverManager.deleteServer(id);
        res.json({
            success: true,
            message: 'Server deleted successfully'
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// Proxy requests to specific server
router.all('/:id/proxy/*', async (req, res) => {
    try {
        const { id } = req.params;
        const server = serverManager.getServer(id);
        if (!server || server.status !== 'running') {
            return res.status(503).json({
                success: false,
                error: 'Server not available'
            });
        }
        const proxyPath = req.params[0];
        const targetUrl = `http://localhost:${server.port}/${proxyPath}`;
        // Forward the request to the target server
        const response = await fetch(targetUrl, {
            method: req.method,
            headers: req.headers,
            body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined
        });
        const data = await response.text();
        res.status(response.status).send(data);
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
// Server health check
router.get('/:id/health', async (req, res) => {
    try {
        const { id } = req.params;
        const server = serverManager.getServer(id);
        if (!server) {
            return res.status(404).json({
                success: false,
                error: 'Server not found'
            });
        }
        if (server.status !== 'running') {
            return res.status(503).json({
                success: false,
                error: 'Server not running',
                status: server.status
            });
        }
        // Check if server is actually responding
        try {
            const response = await fetch(`http://localhost:${server.port}/health`);
            const isHealthy = response.ok;
            res.json({
                success: true,
                healthy: isHealthy,
                server: {
                    id: server.id,
                    name: server.name,
                    status: server.status,
                    port: server.port,
                    lastHealthCheck: server.lastHealthCheck
                }
            });
        }
        catch (error) {
            res.status(503).json({
                success: false,
                healthy: false,
                error: 'Health check failed'
            });
        }
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: error instanceof Error ? error.message : String(error)
        });
    }
});
exports.default = router;
//# sourceMappingURL=servers.js.map