/**
 * MCP manifest generator
 */
import { Parsed<PERSON><PERSON><PERSON><PERSON>, MCPTool, MCPManifest, ServerConfig } from '../types';
export declare class MCPManifestGenerator {
    /**
     * Generate MCP tools from parsed OpenAPI
     */
    generateTools(parsed: ParsedOpenAPI): MCPTool[];
    /**
     * Generate complete MCP manifest
     */
    generateManifest(parsed: ParsedOpenAPI, config: ServerConfig): MCPManifest;
    /**
     * Create MCP tool from OpenAPI endpoint
     */
    private createToolFromEndpoint;
    /**
     * Generate tool name from endpoint
     */
    private generateToolName;
    /**
     * Generate tool description
     */
    private generateToolDescription;
    /**
     * Generate input schema for tool
     */
    private generateInputSchema;
    /**
     * Generate output schema for tool
     */
    private generateOutputSchema;
    /**
     * Validate generated manifest
     */
    validateManifest(manifest: MCPManifest): {
        valid: boolean;
        errors: string[];
    };
    /**
     * Validate individual tool
     */
    private validateTool;
    /**
     * Generate tool statistics
     */
    getToolStats(tools: MCPTool[]): {
        total: number;
        withOutput: number;
        withRequiredInput: number;
        averageInputProperties: number;
    };
    /**
     * Generate manifest summary
     */
    generateSummary(manifest: MCPManifest): string;
}
export default MCPManifestGenerator;
//# sourceMappingURL=mcpManifestGenerator.d.ts.map