pl/**
 * Utility functions for OpenAPI to MCP conversion
 */

import { OpenAPIServer, JSONSchema, OpenAPISchema, HTTPMethod } from './types';

/**
 * Check if a string is a valid URL
 */
export function isValidUrl(string: string): boolean {
  try {
    new URL(string);
    return true;
  } catch {
    return false;
  }
}

/**
 * Validate OpenAPI version
 */
export function validateOpenAPIVersion(version: string): boolean {
  return /^3\.[01]\.\d+$/.test(version);
}

/**
 * Extract base URL from servers array
 */
export function extractBaseUrl(servers?: OpenAPIServer[]): string {
  if (!servers || servers.length === 0) {
    return 'http://localhost:3000';
  }
  
  let baseUrl = servers[0].url;
  
  // Handle server variables (basic replacement)
  baseUrl = baseUrl.replace(/\{[^}]+\}/g, '');
  
  // Ensure it's a valid URL
  try {
    new URL(baseUrl);
    return baseUrl;
  } catch {
    return 'http://localhost:3000';
  }
}

/**
 * Generate operation ID from path and method
 */
export function generateOperationId(path: string, method: string): string {
  // Remove path parameters and split by /
  const pathParts = path
    .split('/')
    .filter(part => part && !part.startsWith('{'))
    .map(part => part.charAt(0).toUpperCase() + part.slice(1));
  
  return method.toLowerCase() + pathParts.join('');
}

/**
 * Convert OpenAPI schema to JSON Schema
 */
export function convertOpenAPISchemaToJSONSchema(schema: OpenAPISchema): JSONSchema {
  const jsonSchema: JSONSchema = {};
  
  // Copy basic properties
  if (schema.type) jsonSchema.type = schema.type as any;
  if (schema.title) jsonSchema.title = schema.title;
  if (schema.description) jsonSchema.description = schema.description;
  if (schema.default !== undefined) jsonSchema.default = schema.default;
  if (schema.example !== undefined) jsonSchema.examples = [schema.example];
  if (schema.enum) jsonSchema.enum = schema.enum;
  if (schema.format) jsonSchema.format = schema.format;
  
  // Handle object properties
  if (schema.properties) {
    jsonSchema.properties = {};
    for (const [key, value] of Object.entries(schema.properties)) {
      jsonSchema.properties[key] = convertOpenAPISchemaToJSONSchema(value);
    }
  }
  
  if (schema.required) jsonSchema.required = schema.required;
  if (schema.additionalProperties !== undefined) {
    if (typeof schema.additionalProperties === 'boolean') {
      jsonSchema.additionalProperties = schema.additionalProperties;
    } else {
      jsonSchema.additionalProperties = convertOpenAPISchemaToJSONSchema(schema.additionalProperties);
    }
  }
  
  // Handle array items
  if (schema.items) {
    jsonSchema.items = convertOpenAPISchemaToJSONSchema(schema.items);
  }
  
  // Handle composition keywords
  if (schema.allOf) {
    jsonSchema.allOf = schema.allOf.map(s => convertOpenAPISchemaToJSONSchema(s));
  }
  if (schema.oneOf) {
    jsonSchema.oneOf = schema.oneOf.map(s => convertOpenAPISchemaToJSONSchema(s));
  }
  if (schema.anyOf) {
    jsonSchema.anyOf = schema.anyOf.map(s => convertOpenAPISchemaToJSONSchema(s));
  }
  if (schema.not) {
    jsonSchema.not = convertOpenAPISchemaToJSONSchema(schema.not);
  }
  
  // Handle reference
  if (schema.$ref) {
    jsonSchema.$ref = schema.$ref;
  }
  
  return jsonSchema;
}

/**
 * Sanitize identifier for use as variable/function names
 */
export function sanitizeIdentifier(input: string): string {
  return input
    .replace(/[^a-zA-Z0-9_]/g, '_')
    .replace(/^[0-9]/, '_$&')
    .replace(/_+/g, '_')
    .replace(/^_+|_+$/g, '');
}

/**
 * Convert string to camelCase
 */
export function toCamelCase(str: string): string {
  return str
    .toLowerCase()
    .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase());
}

/**
 * Generate safe filename from string
 */
export function generateSafeFilename(input: string): string {
  return input
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Ensure directory exists
 */
export async function ensureDir(dirPath: string): Promise<void> {
  const fs = await import('fs-extra');
  await fs.ensureDir(dirPath);
}

/**
 * Check if file exists
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    const fs = await import('fs-extra');
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * Convert OpenAPI path to Express route format
 */
export function convertPathToExpressRoute(path: string): string {
  return path.replace(/{([^}]+)}/g, ':$1');
}

/**
 * Extract path parameters from OpenAPI path
 */
export function extractPathParams(path: string): string[] {
  const matches = path.match(/{([^}]+)}/g);
  if (!matches) return [];
  
  return matches.map(match => match.slice(1, -1));
}

/**
 * Format bytes to human readable string
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Deep merge two objects
 */
export function deepMerge<T>(target: T, source: Partial<T>): T {
  const result = { ...target };
  
  for (const key in source) {
    if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
      result[key] = deepMerge(result[key] || {} as any, source[key] as any);
    } else {
      result[key] = source[key] as any;
    }
  }
  
  return result;
}

/**
 * Generate random ID
 */
export function generateId(length: number = 8): string {
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Sleep for specified milliseconds
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Retry function with exponential backoff
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxAttempts: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (attempt === maxAttempts) {
        throw lastError;
      }
      
      const delay = baseDelay * Math.pow(2, attempt - 1);
      await sleep(delay);
    }
  }
  
  throw lastError!;
}

/**
 * Generate timestamp string
 */
export function getTimestamp(): string {
  return new Date().toISOString().replace(/[:.]/g, '-').slice(0, -1);
}

/**
 * Capitalize first letter of string
 */
export function capitalize(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Convert string to PascalCase
 */
export function toPascalCase(str: string): string {
  return capitalize(toCamelCase(str));
}

/**
 * Validate JSON Schema
 */
export function isValidJSONSchema(schema: any): boolean {
  if (!schema || typeof schema !== 'object') return false;
  
  // Basic validation - check for required properties
  const validTypes = ['string', 'number', 'integer', 'boolean', 'array', 'object', 'null'];
  
  if (schema.type && !validTypes.includes(schema.type)) {
    return false;
  }
  
  return true;
}
