{"ast": null, "code": "var _jsxFileName = \"D:\\\\repos-personal\\\\repos\\\\openapi-to-mcp\\\\ui\\\\src\\\\App.tsx\";\n/**\r\n * Main App component\r\n */\n\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\nimport { Toaster } from 'react-hot-toast';\n\n// Components\nimport { Header } from './components/layout/Header';\nimport { ConversionPage } from './pages/ConversionPage';\n\n// Styles\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50\",\n      children: [/*#__PURE__*/_jsxDEV(<PERSON><PERSON>, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n        className: \"flex-1 p-6\",\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/convert\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 23,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/convert\",\n            element: /*#__PURE__*/_jsxDEV(ConversionPage, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 45\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Toaster, {\n        position: \"top-right\",\n        toastOptions: {\n          duration: 4000,\n          style: {\n            background: '#fff',\n            color: '#374151',\n            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "Toaster", "Header", "ConversionPage", "jsxDEV", "_jsxDEV", "App", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "element", "to", "replace", "position", "toastOptions", "duration", "style", "background", "color", "boxShadow", "_c", "$RefreshReg$"], "sources": ["D:/repos-personal/repos/openapi-to-mcp/ui/src/App.tsx"], "sourcesContent": ["/**\r\n * Main App component\r\n */\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport { Toaster } from 'react-hot-toast';\r\n\r\n// Components\r\nimport { Header } from './components/layout/Header';\r\nimport { ConversionPage } from './pages/ConversionPage';\r\n\r\n// Styles\r\nimport './App.css';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <div className=\"min-h-screen bg-gray-50\">\r\n        <Header />\r\n        <main className=\"flex-1 p-6\">\r\n          <Routes>\r\n            <Route path=\"/\" element={<Navigate to=\"/convert\" replace />} />\r\n            <Route path=\"/convert\" element={<ConversionPage />} />\r\n          </Routes>\r\n        </main>\r\n        {/* Toast notifications */}\r\n        <Toaster \r\n          position=\"top-right\"\r\n          toastOptions={{\r\n            duration: 4000,\r\n            style: {\r\n              background: '#fff',\r\n              color: '#374151',\r\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',\r\n            },\r\n          }}\r\n        />\r\n      </div>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAA+B,OAAO;AAClD,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AACnF,SAASC,OAAO,QAAQ,iBAAiB;;AAEzC;AACA,SAASC,MAAM,QAAQ,4BAA4B;AACnD,SAASC,cAAc,QAAQ,wBAAwB;;AAEvD;AACA,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACR,MAAM;IAAAU,QAAA,eACLF,OAAA;MAAKG,SAAS,EAAC,yBAAyB;MAAAD,QAAA,gBACtCF,OAAA,CAACH,MAAM;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA;QAAMG,SAAS,EAAC,YAAY;QAAAD,QAAA,eAC1BF,OAAA,CAACP,MAAM;UAAAS,QAAA,gBACLF,OAAA,CAACN,KAAK;YAACc,IAAI,EAAC,GAAG;YAACC,OAAO,eAAET,OAAA,CAACL,QAAQ;cAACe,EAAE,EAAC,UAAU;cAACC,OAAO;YAAA;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC/DP,OAAA,CAACN,KAAK;YAACc,IAAI,EAAC,UAAU;YAACC,OAAO,eAAET,OAAA,CAACF,cAAc;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPP,OAAA,CAACJ,OAAO;QACNgB,QAAQ,EAAC,WAAW;QACpBC,YAAY,EAAE;UACZC,QAAQ,EAAE,IAAI;UACdC,KAAK,EAAE;YACLC,UAAU,EAAE,MAAM;YAClBC,KAAK,EAAE,SAAS;YAChBC,SAAS,EAAE;UACb;QACF;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb;AAACY,EAAA,GA1BQlB,GAAG;AA4BZ,eAAeA,GAAG;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}