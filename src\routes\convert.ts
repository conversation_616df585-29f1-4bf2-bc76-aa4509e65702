/**
 * Convert API routes
 */

import { Router, Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import multer from 'multer';
import { OpenAPIParser } from '../core/openapiParser';
import { MCPManifestGenerator } from '../core/mcpManifestGenerator';
import { ServerGenerator } from '../core/serverGenerator';
import { BundleGenerator } from '../core/bundleGenerator';
import { validateConvertRequest } from '../middleware/validation';
import { ServerConfig, CLIOptions } from '../types';
import { isValidUrl, generateSafeFilename } from '../utils';

const router = Router();

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/json',
      'application/x-yaml',
      'text/yaml',
      'text/plain'
    ];
    
    if (allowedTypes.includes(file.mimetype) || 
        file.originalname.match(/\.(json|yaml|yml)$/i)) {
      cb(null, true);
    } else {
      cb(new Error('Only JSON and YAML files are allowed'));
    }
  }
});

// Convert OpenAPI to MCP via JSON payload
router.post('/', validateConvertRequest, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const startTime = Date.now();
    const bundleId = uuidv4();
    
    const {
      openapi,
      config = {}
    } = req.body;

    // Parse OpenAPI specification
    const parser = new OpenAPIParser();
    let parsed;

    if (typeof openapi === 'string') {
      if (isValidUrl(openapi)) {
        // URL provided
        parsed = await parser.parseFromURL(openapi);
      } else {
        // YAML/JSON string provided
        const isYaml = openapi.trim().startsWith('openapi:') || 
                      openapi.trim().startsWith('swagger:');
        parsed = await parser.parseFromString(openapi, isYaml);
      }
    } else if (typeof openapi === 'object') {
      // JSON object provided
      parsed = parser.parseSpec(openapi);
    } else {
      return res.status(400).json({
        error: 'Invalid OpenAPI specification',
        message: 'OpenAPI must be a URL, YAML string, or JSON object'
      });
    }

    // Create server configuration
    const serverConfig: ServerConfig = {
      name: config.name || generateSafeFilename(parsed.spec.info.title) || 'mcp-server',
      version: config.version || parsed.spec.info.version || '1.0.0',
      description: config.description || parsed.spec.info.description || 'Generated MCP server',
      port: config.port || 8000,
      baseUrl: config.baseUrl || parsed.baseUrl,
      outputDir: `/tmp/mcp-bundles/${bundleId}`,
      author: config.author,
      license: config.license || 'MIT'
    };

    // Generate MCP manifest
    const manifestGenerator = new MCPManifestGenerator();
    const manifest = manifestGenerator.generateManifest(parsed, serverConfig);

    // Generate server code
    const serverGenerator = new ServerGenerator();
    const generatedFiles = await serverGenerator.generateServer(parsed, serverConfig);

    // Create bundle
    const bundleGenerator = new BundleGenerator();
    const bundlePath = await bundleGenerator.createBundle(bundleId, {
      manifest,
      files: generatedFiles,
      config: serverConfig
    });

    const endTime = Date.now();
    const generationTime = ((endTime - startTime) / 1000).toFixed(2);

    // Get statistics
    const stats = parser.getSpecStats(parsed);
    const toolStats = manifestGenerator.getToolStats(manifest.tools);

    res.json({
      success: true,
      bundleId,
      manifest,
      serverCode: generatedFiles['src/server.ts'],
      files: {
        'package.json': generatedFiles['package.json'],
        'tsconfig.json': generatedFiles['tsconfig.json'],
        'src/server.ts': generatedFiles['src/server.ts'],
        'src/types.ts': generatedFiles['src/types.ts'],
        'mcp.json': JSON.stringify(manifest, null, 2)
      },
      downloadUrl: `/api/download/${bundleId}`,
      stats: {
        endpoints: stats.endpoints,
        tools: toolStats.total,
        generationTime: `${generationTime}s`,
        bundleSize: await bundleGenerator.getBundleSize(bundlePath)
      }
    });

  } catch (error) {
    next(error);
  }
});

// Convert OpenAPI to MCP via file upload
router.post('/upload', upload.single('openapi'), async (req: Request, res: Response, next: NextFunction) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please upload an OpenAPI specification file'
      });
    }

    const startTime = Date.now();
    const bundleId = uuidv4();
    
    const config = req.body.config ? JSON.parse(req.body.config) : {};

    // Parse uploaded file
    const parser = new OpenAPIParser();
    const content = req.file.buffer.toString('utf-8');
    const isYaml = req.file.originalname.match(/\.(yaml|yml)$/i) !== null;
    
    const parsed = await parser.parseFromString(content, isYaml);

    // Create server configuration
    const serverConfig: ServerConfig = {
      name: config.name || generateSafeFilename(parsed.spec.info.title) || 'mcp-server',
      version: config.version || parsed.spec.info.version || '1.0.0',
      description: config.description || parsed.spec.info.description || 'Generated MCP server',
      port: config.port || 8000,
      baseUrl: config.baseUrl || parsed.baseUrl,
      outputDir: `/tmp/mcp-bundles/${bundleId}`,
      author: config.author,
      license: config.license || 'MIT'
    };

    // Generate MCP manifest
    const manifestGenerator = new MCPManifestGenerator();
    const manifest = manifestGenerator.generateManifest(parsed, serverConfig);

    // Generate server code
    const serverGenerator = new ServerGenerator();
    const generatedFiles = await serverGenerator.generateServer(parsed, serverConfig);

    // Create bundle
    const bundleGenerator = new BundleGenerator();
    const bundlePath = await bundleGenerator.createBundle(bundleId, {
      manifest,
      files: generatedFiles,
      config: serverConfig
    });

    const endTime = Date.now();
    const generationTime = ((endTime - startTime) / 1000).toFixed(2);

    // Get statistics
    const stats = parser.getSpecStats(parsed);
    const toolStats = manifestGenerator.getToolStats(manifest.tools);

    res.json({
      success: true,
      bundleId,
      manifest,
      serverCode: generatedFiles['src/server.ts'],
      files: {
        'package.json': generatedFiles['package.json'],
        'tsconfig.json': generatedFiles['tsconfig.json'],
        'src/server.ts': generatedFiles['src/server.ts'],
        'src/types.ts': generatedFiles['src/types.ts'],
        'mcp.json': JSON.stringify(manifest, null, 2)
      },
      downloadUrl: `/api/download/${bundleId}`,
      stats: {
        endpoints: stats.endpoints,
        tools: toolStats.total,
        generationTime: `${generationTime}s`,
        bundleSize: await bundleGenerator.getBundleSize(bundlePath),
        originalFile: {
          name: req.file.originalname,
          size: req.file.size,
          type: req.file.mimetype
        }
      }
    });

  } catch (error) {
    next(error);
  }
});

// Get conversion status (for long-running operations)
router.get('/status/:bundleId', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { bundleId } = req.params;
    
    // Check if bundle exists
    const bundleGenerator = new BundleGenerator();
    const exists = await bundleGenerator.bundleExists(bundleId);
    
    if (!exists) {
      return res.status(404).json({
        error: 'Bundle not found',
        message: `Bundle with ID ${bundleId} does not exist or has expired`
      });
    }

    const bundlePath = await bundleGenerator.getBundlePath(bundleId);
    const bundleSize = await bundleGenerator.getBundleSize(bundlePath);

    res.json({
      bundleId,
      status: 'completed',
      downloadUrl: `/api/download/${bundleId}`,
      bundleSize,
      createdAt: new Date().toISOString() // TODO: Get actual creation time
    });

  } catch (error) {
    next(error);
  }
});

export { router as convertRoutes };
export default router;
