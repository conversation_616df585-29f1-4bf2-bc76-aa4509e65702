"use strict";
/**
 * OpenAPI specification parser
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OpenAPIParser = void 0;
const fs = __importStar(require("fs-extra"));
const yaml = __importStar(require("js-yaml"));
const axios_1 = __importDefault(require("axios"));
const types_1 = require("../types");
const utils_1 = require("../utils");
class OpenAPIParser {
    /**
     * Parse OpenAPI specification from file
     */
    async parseFromFile(filePath) {
        try {
            const content = await fs.readFile(filePath, 'utf-8');
            const isYaml = filePath.endsWith('.yaml') || filePath.endsWith('.yml');
            const spec = isYaml ? yaml.load(content) : JSON.parse(content);
            return this.parseSpec(spec);
        }
        catch (error) {
            throw new types_1.OpenAPIParseError(`Failed to parse OpenAPI file: ${filePath}`, error);
        }
    }
    /**
     * Parse OpenAPI specification from URL
     */
    async parseFromURL(url) {
        try {
            const response = await axios_1.default.get(url, {
                timeout: 10000,
                headers: {
                    'Accept': 'application/json, application/x-yaml, text/yaml'
                }
            });
            let spec;
            const contentType = response.headers['content-type'] || '';
            if (contentType.includes('yaml') || contentType.includes('yml')) {
                spec = yaml.load(response.data);
            }
            else {
                spec = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
            }
            return this.parseSpec(spec);
        }
        catch (error) {
            throw new types_1.OpenAPIParseError(`Failed to fetch OpenAPI spec from URL: ${url}`, error);
        }
    }
    /**
     * Parse OpenAPI specification from string content
     */
    async parseFromString(content, isYaml = false) {
        try {
            const spec = isYaml ? yaml.load(content) : JSON.parse(content);
            return this.parseSpec(spec);
        }
        catch (error) {
            throw new types_1.OpenAPIParseError('Failed to parse OpenAPI content', error);
        }
    }
    /**
     * Parse OpenAPI specification object
     */
    parseSpec(spec) {
        // Validate the specification
        const validation = this.validateSpec(spec);
        if (!validation.valid) {
            throw new types_1.OpenAPIParseError(`Invalid OpenAPI specification: ${validation.errors.join(', ')}`);
        }
        // Extract base URL
        const baseUrl = (0, utils_1.extractBaseUrl)(spec.servers);
        // Parse endpoints
        const endpoints = this.parseEndpoints(spec);
        return {
            spec,
            baseUrl,
            endpoints
        };
    }
    /**
     * Validate OpenAPI specification
     */
    validateSpec(spec) {
        const errors = [];
        const warnings = [];
        // Check if spec is an object
        if (!spec || typeof spec !== 'object') {
            errors.push('Specification must be an object');
            return { valid: false, errors, warnings };
        }
        // Check OpenAPI version
        if (!spec.openapi) {
            errors.push('Missing openapi field');
        }
        else if (!(0, utils_1.validateOpenAPIVersion)(spec.openapi)) {
            errors.push(`Unsupported OpenAPI version: ${spec.openapi}. Supported versions: 3.0.x, 3.1.x`);
        }
        // Check info object
        if (!spec.info) {
            errors.push('Missing info object');
        }
        else {
            if (!spec.info.title) {
                errors.push('Missing info.title');
            }
            if (!spec.info.version) {
                errors.push('Missing info.version');
            }
        }
        // Check paths object
        if (!spec.paths) {
            errors.push('Missing paths object');
        }
        else if (typeof spec.paths !== 'object') {
            errors.push('Paths must be an object');
        }
        else if (Object.keys(spec.paths).length === 0) {
            warnings.push('No paths defined in specification');
        }
        // Validate servers
        if (spec.servers && !Array.isArray(spec.servers)) {
            errors.push('Servers must be an array');
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }
    /**
     * Parse all endpoints from OpenAPI paths
     */
    parseEndpoints(spec) {
        const endpoints = [];
        for (const [path, pathItem] of Object.entries(spec.paths)) {
            if (!pathItem)
                continue;
            const httpMethods = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace'];
            for (const method of httpMethods) {
                const operation = pathItem[method];
                if (!operation)
                    continue;
                const endpoint = {
                    path,
                    method,
                    operationId: operation.operationId || (0, utils_1.generateOperationId)(path, method),
                    summary: operation.summary,
                    description: operation.description,
                    parameters: this.parseParameters(operation.parameters || [], pathItem.parameters || []),
                    requestBody: operation.requestBody ? this.parseRequestBody(operation.requestBody) : undefined,
                    responses: this.parseResponses(operation.responses),
                    tags: operation.tags
                };
                endpoints.push(endpoint);
            }
        }
        return endpoints;
    }
    /**
     * Parse operation parameters
     */
    parseParameters(operationParams = [], pathParams = []) {
        const allParams = [...pathParams, ...operationParams];
        const parsedParams = [];
        for (const param of allParams) {
            if (!param.name || !param.in)
                continue;
            const parsedParam = {
                name: param.name,
                in: param.in,
                description: param.description,
                required: param.required || param.in === 'path',
                schema: param.schema ? (0, utils_1.convertOpenAPISchemaToJSONSchema)(param.schema) : { type: 'string' },
                example: param.example
            };
            parsedParams.push(parsedParam);
        }
        return parsedParams;
    }
    /**
     * Parse request body
     */
    parseRequestBody(requestBody) {
        if (!requestBody.content)
            return undefined;
        // Find the first supported content type
        const supportedTypes = ['application/json', 'application/x-www-form-urlencoded', 'multipart/form-data'];
        let contentType = 'application/json';
        let mediaType = requestBody.content['application/json'];
        for (const type of supportedTypes) {
            if (requestBody.content[type]) {
                contentType = type;
                mediaType = requestBody.content[type];
                break;
            }
        }
        if (!mediaType) {
            // Use the first available content type
            const firstType = Object.keys(requestBody.content)[0];
            if (firstType) {
                contentType = firstType;
                mediaType = requestBody.content[firstType];
            }
        }
        return {
            description: requestBody.description,
            required: requestBody.required || false,
            contentType,
            schema: mediaType?.schema ? (0, utils_1.convertOpenAPISchemaToJSONSchema)(mediaType.schema) : { type: 'object' }
        };
    }
    /**
     * Parse operation responses
     */
    parseResponses(responses) {
        const parsedResponses = [];
        for (const [status, response] of Object.entries(responses)) {
            if (!response || typeof response !== 'object')
                continue;
            const parsedResponse = {
                status,
                description: response.description || '',
                contentType: undefined,
                schema: undefined
            };
            // Parse response content
            const content = response.content;
            if (content) {
                // Prefer JSON response
                if (content['application/json']) {
                    parsedResponse.contentType = 'application/json';
                    parsedResponse.schema = content['application/json'].schema
                        ? (0, utils_1.convertOpenAPISchemaToJSONSchema)(content['application/json'].schema)
                        : undefined;
                }
                else {
                    // Use first available content type
                    const firstType = Object.keys(content)[0];
                    if (firstType) {
                        parsedResponse.contentType = firstType;
                        parsedResponse.schema = content[firstType].schema
                            ? (0, utils_1.convertOpenAPISchemaToJSONSchema)(content[firstType].schema)
                            : undefined;
                    }
                }
            }
            parsedResponses.push(parsedResponse);
        }
        return parsedResponses;
    }
    /**
     * Extract all tags from the specification
     */
    extractTags(spec) {
        const tags = new Set();
        // Add tags from global tags array
        if (spec.tags) {
            for (const tag of spec.tags) {
                if (tag.name) {
                    tags.add(tag.name);
                }
            }
        }
        // Add tags from operations
        for (const pathItem of Object.values(spec.paths)) {
            if (!pathItem)
                continue;
            const httpMethods = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace'];
            for (const method of httpMethods) {
                const operation = pathItem[method];
                if (operation?.tags) {
                    for (const tag of operation.tags) {
                        tags.add(tag);
                    }
                }
            }
        }
        return Array.from(tags).sort();
    }
    /**
     * Get statistics about the OpenAPI specification
     */
    getSpecStats(parsed) {
        const stats = {
            endpoints: parsed.endpoints.length,
            methods: {},
            tags: this.extractTags(parsed.spec),
            hasAuthentication: !!(parsed.spec.components?.securitySchemes || parsed.spec.security),
            hasRequestBodies: 0,
            hasParameters: 0
        };
        // Count methods and features
        for (const endpoint of parsed.endpoints) {
            stats.methods[endpoint.method] = (stats.methods[endpoint.method] || 0) + 1;
            if (endpoint.requestBody) {
                stats.hasRequestBodies++;
            }
            if (endpoint.parameters.length > 0) {
                stats.hasParameters++;
            }
        }
        return stats;
    }
}
exports.OpenAPIParser = OpenAPIParser;
// Default export for convenience
exports.default = OpenAPIParser;
//# sourceMappingURL=openapiParser.js.map