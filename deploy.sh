#!/bin/bash

# OpenAPI to MCP Server Deployment Script for Railway

set -e

echo "🚀 Deploying OpenAPI to MCP Server to Railway..."

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it first:"
    echo "npm install -g @railway/cli"
    exit 1
fi

# Check if user is logged in
if ! railway whoami &> /dev/null; then
    echo "🔐 Please login to Railway first:"
    railway login
fi

# Build the project
echo "🔨 Building project..."
npm run build

# Deploy to Railway
echo "🚀 Deploying to Railway..."
railway up

# Set environment variables
echo "⚙️  Setting environment variables..."
railway variables set NODE_ENV=production
railway variables set PORT=3000

# Optional: Set LiteLLM configuration
read -p "Do you want to configure LiteLLM? (y/n): " configure_llm
if [[ $configure_llm == "y" || $configure_llm == "Y" ]]; then
    read -p "Enter LiteLLM URL (default: https://litellm-production-744f.up.railway.app/chat/completions): " llm_url
    read -p "Enter LiteLLM Model (default: deepseek-chat): " llm_model
    read -p "Enter LiteLLM API Key: " llm_key
    
    railway variables set LITELLM_URL="${llm_url:-https://litellm-production-744f.up.railway.app/chat/completions}"
    railway variables set LITELLM_MODEL="${llm_model:-deepseek-chat}"
    railway variables set LITELLM_API_KEY="$llm_key"
fi

# Get the deployment URL
echo "🌐 Getting deployment URL..."
RAILWAY_URL=$(railway status --json | jq -r '.deployments[0].url' 2>/dev/null || echo "")

if [[ -n "$RAILWAY_URL" ]]; then
    echo "✅ Deployment successful!"
    echo "🌐 Your OpenAPI to MCP Server is available at: $RAILWAY_URL"
    echo ""
    echo "📚 API Endpoints:"
    echo "  - Health Check: $RAILWAY_URL/api/health"
    echo "  - Convert OpenAPI: $RAILWAY_URL/api/convert"
    echo "  - Server Management: $RAILWAY_URL/api/servers"
    echo "  - Chat Interface: $RAILWAY_URL/api/chat"
    echo ""
    echo "🔧 Server Management:"
    echo "  - List servers: GET $RAILWAY_URL/api/servers"
    echo "  - Create server: POST $RAILWAY_URL/api/servers"
    echo "  - Server proxy: $RAILWAY_URL/api/servers/{id}/proxy/*"
else
    echo "⚠️  Deployment completed but couldn't retrieve URL. Check Railway dashboard."
fi

echo ""
echo "🎉 Deployment complete! Check your Railway dashboard for more details."
