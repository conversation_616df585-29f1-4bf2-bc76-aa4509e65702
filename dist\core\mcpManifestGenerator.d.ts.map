{"version": 3, "file": "mcpManifestGenerator.d.ts", "sourceRoot": "", "sources": ["../../src/core/mcpManifestGenerator.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EACL,aAAa,EAGb,OAAO,EACP,WAAW,EAGX,YAAY,EACb,MAAM,UAAU,CAAC;AAGlB,qBAAa,oBAAoB;IAC/B;;OAEG;IACH,aAAa,CAAC,MAAM,EAAE,aAAa,GAAG,OAAO,EAAE;IAW/C;;OAEG;IACH,gBAAgB,CAAC,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,YAAY,GAAG,WAAW;IA8B1E;;OAEG;IACH,OAAO,CAAC,sBAAsB;IAc9B;;OAEG;IACH,OAAO,CAAC,gBAAgB;IAmBxB;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAgB/B;;OAEG;IACH,OAAO,CAAC,mBAAmB;IA6F3B;;OAEG;IACH,OAAO,CAAC,oBAAoB;IAsC5B;;OAEG;IACH,gBAAgB,CAAC,QAAQ,EAAE,WAAW,GAAG;QAAE,KAAK,EAAE,OAAO,CAAC;QAAC,MAAM,EAAE,MAAM,EAAE,CAAA;KAAE;IA2C7E;;OAEG;IACH,OAAO,CAAC,YAAY;IAqBpB;;OAEG;IACH,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,GAAG;QAC9B,KAAK,EAAE,MAAM,CAAC;QACd,UAAU,EAAE,MAAM,CAAC;QACnB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,sBAAsB,EAAE,MAAM,CAAC;KAChC;IA+BD;;OAEG;IACH,eAAe,CAAC,QAAQ,EAAE,WAAW,GAAG,MAAM;CAa/C;AAGD,eAAe,oBAAoB,CAAC"}