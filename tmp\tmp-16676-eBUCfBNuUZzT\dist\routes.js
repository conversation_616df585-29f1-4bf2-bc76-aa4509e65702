"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.router = void 0;
const express_1 = require("express");
const axios_1 = __importDefault(require("axios"));
const https_proxy_agent_1 = require("https-proxy-agent");
const http_proxy_agent_1 = require("http-proxy-agent");
const router = (0, express_1.Router)();
exports.router = router;
const BASE_URL = process.env.BASE_URL || 'http://localhost:8000';
// Proxy configuration utilities
function getProxyConfig() {
    return {
        httpProxy: process.env.HTTP_PROXY || process.env.http_proxy,
        httpsProxy: process.env.HTTPS_PROXY || process.env.https_proxy,
        noProxy: process.env.NO_PROXY || process.env.no_proxy
    };
}
function shouldBypassProxy(url, noProxy) {
    if (!noProxy)
        return false;
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;
    const noProxyList = noProxy.split(',').map(item => item.trim().toLowerCase());
    for (const pattern of noProxyList) {
        if (!pattern)
            continue;
        if (hostname === pattern)
            return true;
        if (pattern.startsWith('*.') && hostname.endsWith(pattern.slice(2)))
            return true;
        if (pattern.startsWith('.') && hostname.endsWith(pattern))
            return true;
        if (pattern === 'localhost' && (hostname === 'localhost' || hostname === '127.0.0.1'))
            return true;
    }
    return false;
}
function createProxyAgent(url) {
    const config = getProxyConfig();
    const urlObj = new URL(url);
    if (shouldBypassProxy(url, config.noProxy))
        return undefined;
    const isHttps = urlObj.protocol === 'https:';
    const proxyUrl = isHttps ? config.httpsProxy : config.httpProxy;
    if (!proxyUrl)
        return undefined;
    console.log(`[PROXY] Using ${isHttps ? 'HTTPS' : 'HTTP'} proxy: ${proxyUrl} for ${url}`);
    try {
        return isHttps ? new https_proxy_agent_1.HttpsProxyAgent(proxyUrl) : new http_proxy_agent_1.HttpProxyAgent(proxyUrl);
    }
    catch (error) {
        console.error(`[PROXY] Failed to create proxy agent for ${proxyUrl}:`, error);
        return undefined;
    }
}
function getAxiosConfigWithProxy(url, config = {}) {
    const agent = createProxyAgent(url);
    if (agent) {
        return {
            ...config,
            httpsAgent: agent,
            httpAgent: agent
        };
    }
    return config;
}
// Add a new pet to the store.
router.post('/addPet', async (req, res) => {
    try {
        const { body } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet`;
        // Prepare request configuration
        const config = {
            method: 'POST',
            url,
            timeout: 30000
        };
        // Add request body
        if (body) {
            config.data = body;
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/json'
            };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('addPet error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Update an existing pet.
router.post('/updatePet', async (req, res) => {
    try {
        const { body } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet`;
        // Prepare request configuration
        const config = {
            method: 'PUT',
            url,
            timeout: 30000
        };
        // Add request body
        if (body) {
            config.data = body;
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/json'
            };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('updatePet error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Finds Pets by status.
router.post('/findPetsByStatus', async (req, res) => {
    try {
        const { query } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet/findByStatus`;
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Add query parameters
        if (query) {
            config.params = query;
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('findPetsByStatus error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Finds Pets by tags.
router.post('/findPetsByTags', async (req, res) => {
    try {
        const { query } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet/findByTags`;
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Add query parameters
        if (query) {
            config.params = query;
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('findPetsByTags error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Find pet by ID.
router.post('/getPetById', async (req, res) => {
    try {
        const { petId } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet/{petId}`;
        url = url.replace('petId', encodeURIComponent(petId));
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('getPetById error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Updates a pet in the store with form data.
router.post('/updatePetWithForm', async (req, res) => {
    try {
        const { petId, query } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet/{petId}`;
        url = url.replace('petId', encodeURIComponent(petId));
        // Prepare request configuration
        const config = {
            method: 'POST',
            url,
            timeout: 30000
        };
        // Add query parameters
        if (query) {
            config.params = query;
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('updatePetWithForm error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Deletes a pet.
router.post('/deletePet', async (req, res) => {
    try {
        const { petId, headers } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet/{petId}`;
        url = url.replace('petId', encodeURIComponent(petId));
        // Prepare request configuration
        const config = {
            method: 'DELETE',
            url,
            timeout: 30000
        };
        // Add headers
        if (headers) {
            config.headers = { ...config.headers, ...headers };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('deletePet error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Uploads an image.
router.post('/uploadFile', async (req, res) => {
    try {
        const { petId, query, body } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/pet/{petId}/uploadImage`;
        url = url.replace('petId', encodeURIComponent(petId));
        // Prepare request configuration
        const config = {
            method: 'POST',
            url,
            timeout: 30000
        };
        // Add query parameters
        if (query) {
            config.params = query;
        }
        // Add request body
        if (body) {
            config.data = body;
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/octet-stream'
            };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('uploadFile error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Returns pet inventories by status.
router.post('/getInventory', async (req, res) => {
    try {
        const {} = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/store/inventory`;
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('getInventory error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Place an order for a pet.
router.post('/placeOrder', async (req, res) => {
    try {
        const { body } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/store/order`;
        // Prepare request configuration
        const config = {
            method: 'POST',
            url,
            timeout: 30000
        };
        // Add request body
        if (body) {
            config.data = body;
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/json'
            };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('placeOrder error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Find purchase order by ID.
router.post('/getOrderById', async (req, res) => {
    try {
        const { orderId } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/store/order/{orderId}`;
        url = url.replace('orderId', encodeURIComponent(orderId));
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('getOrderById error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Delete purchase order by identifier.
router.post('/deleteOrder', async (req, res) => {
    try {
        const { orderId } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/store/order/{orderId}`;
        url = url.replace('orderId', encodeURIComponent(orderId));
        // Prepare request configuration
        const config = {
            method: 'DELETE',
            url,
            timeout: 30000
        };
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('deleteOrder error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Create user.
router.post('/createUser', async (req, res) => {
    try {
        const { body } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/user`;
        // Prepare request configuration
        const config = {
            method: 'POST',
            url,
            timeout: 30000
        };
        // Add request body
        if (body) {
            config.data = body;
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/json'
            };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('createUser error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Creates list of users with given input array.
router.post('/createUsersWithListInput', async (req, res) => {
    try {
        const { body } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/user/createWithList`;
        // Prepare request configuration
        const config = {
            method: 'POST',
            url,
            timeout: 30000
        };
        // Add request body
        if (body) {
            config.data = body;
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/json'
            };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('createUsersWithListInput error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Logs user into the system.
router.post('/loginUser', async (req, res) => {
    try {
        const { query } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/user/login`;
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Add query parameters
        if (query) {
            config.params = query;
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('loginUser error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Logs out current logged in user session.
router.post('/logoutUser', async (req, res) => {
    try {
        const {} = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/user/logout`;
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('logoutUser error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Get user by user name.
router.post('/getUserByName', async (req, res) => {
    try {
        const { username } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/user/{username}`;
        url = url.replace('username', encodeURIComponent(username));
        // Prepare request configuration
        const config = {
            method: 'GET',
            url,
            timeout: 30000
        };
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('getUserByName error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Update user resource.
router.post('/updateUser', async (req, res) => {
    try {
        const { username, body } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/user/{username}`;
        url = url.replace('username', encodeURIComponent(username));
        // Prepare request configuration
        const config = {
            method: 'PUT',
            url,
            timeout: 30000
        };
        // Add request body
        if (body) {
            config.data = body;
            config.headers = {
                ...config.headers,
                'Content-Type': 'application/json'
            };
        }
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('updateUser error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Delete user resource.
router.post('/deleteUser', async (req, res) => {
    try {
        const { username } = req.body;
        // Build URL with path parameters
        let url = `${BASE_URL}/user/{username}`;
        url = url.replace('username', encodeURIComponent(username));
        // Prepare request configuration
        const config = {
            method: 'DELETE',
            url,
            timeout: 30000
        };
        // Make API request with proxy support
        const finalConfig = getAxiosConfigWithProxy(config.url, config);
        const response = await (0, axios_1.default)(finalConfig);
        res.json({
            success: true,
            data: response.data,
            status: response.status,
            headers: response.headers
        });
    }
    catch (error) {
        console.error('deleteUser error:', error.message);
        if (error.response) {
            // API error
            res.status(error.response.status || 500).json({
                success: false,
                error: 'API Error',
                message: error.response.data?.message || error.message,
                status: error.response.status,
                data: error.response.data
            });
        }
        else {
            // Network or other error
            res.status(500).json({
                success: false,
                error: 'Request Failed',
                message: error.message
            });
        }
    }
});
// Generic LLM alias endpoints for robustness across all APIs
// These handle common LLM mistakes and provide better user experience
// Catch-all for missing endpoints - try to find a close match
router.use('*', async (req, res, next) => {
    if (req.method !== 'POST')
        return next();
    const requestedPath = req.path.replace('/tools/', '').replace('/', '');
    // Get all actual endpoint names from this router
    const actualEndpoints = router.stack
        .filter((layer) => layer.route && layer.route.path !== '*')
        .map((layer) => layer.route.path.replace('/', ''));
    // Try to find a close match using common patterns
    let bestMatch = null;
    // Common LLM naming patterns and their likely targets
    const patterns = [
        // "find" variations
        { pattern: /^(find|list|get|search|query)(.+)$/, target: (match) => actualEndpoints.find(ep => ep.includes(match.toLowerCase())) },
        // "create" variations  
        { pattern: /^(add|insert|new|create)(.+)$/, target: (match) => actualEndpoints.find(ep => ep.includes('create') || ep.includes('add')) },
        // "update" variations
        { pattern: /^(edit|modify|change|update)(.+)$/, target: (match) => actualEndpoints.find(ep => ep.includes('update') || ep.includes('edit')) },
        // "delete" variations
        { pattern: /^(remove|destroy|delete)(.+)$/, target: (match) => actualEndpoints.find(ep => ep.includes('delete') || ep.includes('remove')) }
    ];
    for (const { pattern, target } of patterns) {
        const match = requestedPath.match(pattern);
        if (match) {
            const baseWord = match[2];
            bestMatch = target(baseWord) || actualEndpoints.find(ep => ep.toLowerCase().includes(baseWord.toLowerCase()) ||
                baseWord.toLowerCase().includes(ep.toLowerCase().split(/(?=[A-Z])/).join('').toLowerCase()));
            if (bestMatch)
                break;
        }
    }
    // If no pattern match, try exact substring matching
    if (!bestMatch) {
        bestMatch = actualEndpoints.find(ep => ep.toLowerCase().includes(requestedPath.toLowerCase()) ||
            requestedPath.toLowerCase().includes(ep.toLowerCase()));
    }
    if (bestMatch) {
        console.log(`[ALIAS] Redirecting /${requestedPath} to /${bestMatch}`);
        req.url = `/${bestMatch}`;
        // req.path is read-only in Express, so do not assign to it!
        return next('route');
    }
    // If no match found, return helpful error with available endpoints
    res.status(404).json({
        error: 'Tool not found',
        message: `Tool '${requestedPath}' not found.`,
        availableTools: actualEndpoints,
        suggestion: `Did you mean one of: ${actualEndpoints.slice(0, 5).join(', ')}?`
    });
});
exports.default = router;
//# sourceMappingURL=routes.js.map