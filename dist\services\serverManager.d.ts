/**
 * MCP Server Manager - Handles multiple running MCP servers
 */
import { EventEmitter } from 'events';
export interface MCPServerInfo {
    id: string;
    name: string;
    port: number;
    status: 'starting' | 'running' | 'stopped' | 'error';
    pid?: number;
    baseUrl: string;
    openApiUrl?: string;
    createdAt: Date;
    lastHealthCheck?: Date;
    errorMessage?: string;
}
export declare class ServerManager extends EventEmitter {
    private servers;
    private processes;
    private basePort;
    private maxServers;
    private serversDir;
    constructor();
    /**
     * Create and start a new MCP server
     */
    createServer(name: string, openApiUrl: string, baseUrl: string, config?: any): Promise<MCPServerInfo>;
    /**
     * Stop a running MCP server
     */
    stopServer(id: string): Promise<void>;
    /**
     * Restart a MCP server
     */
    restartServer(id: string): Promise<void>;
    /**
     * Get all servers
     */
    getAllServers(): MCPServerInfo[];
    /**
     * Get server by ID
     */
    getServer(id: string): MCPServerInfo | undefined;
    /**
     * Delete a server completely
     */
    deleteServer(id: string): Promise<void>;
    /**
     * Generate server files using the OpenAPI generator
     */
    private generateServerFiles;
    /**
     * Start server process
     */
    private startServerProcess;
    /**
     * Generate unique server ID
     */
    private generateServerId;
    /**
     * Get next available port
     */
    private getNextAvailablePort;
    /**
     * Ensure servers directory exists
     */
    private ensureServersDirectory;
    /**
     * Start health check interval
     */
    private startHealthCheckInterval;
    /**
     * Generate Dockerfile for individual MCP server
     */
    private generateDockerfile;
}
export default ServerManager;
//# sourceMappingURL=serverManager.d.ts.map