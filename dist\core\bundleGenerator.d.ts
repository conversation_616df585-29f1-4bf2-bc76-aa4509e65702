/**
 * Bundle generator for creating downloadable ZIP files
 */
import { MCPManifest, ServerConfig } from '../types';
export interface BundleContent {
    manifest: MCPManifest;
    files: Record<string, string>;
    config: ServerConfig;
}
export declare class BundleGenerator {
    private bundleDir;
    constructor();
    /**
     * Create a ZIP bundle with all generated files
     */
    createBundle(bundleId: string, content: BundleContent): Promise<string>;
    /**
     * Write all generated files to temporary directory
     */
    private writeFilesToTemp;
    /**
     * Write bundle metadata
     */
    private writeMetadata;
    /**
     * Generate installation instructions
     */
    private generateInstallationInstructions;
    /**
     * Create ZIP archive from directory
     */
    private createZipArchive;
    /**
     * Check if bundle exists
     */
    bundleExists(bundleId: string): Promise<boolean>;
    /**
     * Get bundle file path
     */
    getBundlePath(bundleId: string): Promise<string>;
    /**
     * Get bundle file size
     */
    getBundleSize(bundlePath: string): Promise<string>;
    /**
     * Delete bundle file
     */
    deleteBundle(bundleId: string): Promise<boolean>;
    /**
     * List all bundles
     */
    listBundles(): Promise<Array<{
        id: string;
        path: string;
        size: string;
        created: Date;
    }>>;
    /**
     * Clean up old bundles (older than specified hours)
     */
    cleanupOldBundles(maxAgeHours?: number): Promise<number>;
    /**
     * Get total storage usage
     */
    getStorageUsage(): Promise<{
        totalFiles: number;
        totalSize: string;
    }>;
    /**
     * Ensure bundle directory exists
     */
    private ensureBundleDir;
    /**
     * Create bundle stream for direct download without saving to disk
     */
    createBundleStream(content: BundleContent): Promise<NodeJS.ReadableStream>;
}
export default BundleGenerator;
//# sourceMappingURL=bundleGenerator.d.ts.map