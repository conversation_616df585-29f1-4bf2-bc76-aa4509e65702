/**
 * Server management routes for multiple MCP servers
 */

import { Router, Request, Response } from 'express';
import ServerManager from '../services/serverManager';
import { getFetchOptionsWithProxy } from '../utils/proxy';

const router = Router();
const serverManager = new ServerManager();

// Get all servers
router.get('/', async (req: Request, res: Response) => {
  try {
    const servers = serverManager.getAllServers();
    res.json({
      success: true,
      servers,
      count: servers.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Get specific server
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const server = serverManager.getServer(id);
    
    if (!server) {
      return res.status(404).json({
        success: false,
        error: 'Server not found'
      });
    }

    res.json({
      success: true,
      server
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Create new server
router.post('/', async (req: Request, res: Response) => {
  try {
    const { name, openApiUrl, baseUrl, config } = req.body;

    if (!name || !openApiUrl || !baseUrl) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: name, openApiUrl, baseUrl'
      });
    }

    const server = await serverManager.createServer(name, openApiUrl, baseUrl, config);
    
    res.status(201).json({
      success: true,
      server,
      message: 'Server created successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Stop server
router.post('/:id/stop', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await serverManager.stopServer(id);
    
    res.json({
      success: true,
      message: 'Server stopped successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Restart server
router.post('/:id/restart', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await serverManager.restartServer(id);
    
    res.json({
      success: true,
      message: 'Server restarted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Delete server
router.delete('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    await serverManager.deleteServer(id);
    
    res.json({
      success: true,
      message: 'Server deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Proxy requests to specific server
router.all('/:id/proxy/*', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const server = serverManager.getServer(id);
    
    if (!server || server.status !== 'running') {
      return res.status(503).json({
        success: false,
        error: 'Server not available'
      });
    }

    const proxyPath = req.params[0];
    const targetUrl = `http://localhost:${server.port}/${proxyPath}`;
    
    // Forward the request to the target server with proxy support
    const fetchOptions = getFetchOptionsWithProxy(targetUrl, {
      method: req.method,
      headers: req.headers as any,
      body: req.method !== 'GET' && req.method !== 'HEAD' ? JSON.stringify(req.body) : undefined
    });

    const response = await fetch(targetUrl, fetchOptions);

    const data = await response.text();
    res.status(response.status).send(data);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

// Server health check
router.get('/:id/health', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const server = serverManager.getServer(id);
    
    if (!server) {
      return res.status(404).json({
        success: false,
        error: 'Server not found'
      });
    }

    if (server.status !== 'running') {
      return res.status(503).json({
        success: false,
        error: 'Server not running',
        status: server.status
      });
    }

    // Check if server is actually responding
    try {
      const healthUrl = `http://localhost:${server.port}/health`;
      const fetchOptions = getFetchOptionsWithProxy(healthUrl);
      const response = await fetch(healthUrl, fetchOptions);
      const isHealthy = response.ok;
      
      res.json({
        success: true,
        healthy: isHealthy,
        server: {
          id: server.id,
          name: server.name,
          status: server.status,
          port: server.port,
          lastHealthCheck: server.lastHealthCheck
        }
      });
    } catch (error) {
      res.status(503).json({
        success: false,
        healthy: false,
        error: 'Health check failed'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error)
    });
  }
});

export { router as serversRoutes };
export default router;
