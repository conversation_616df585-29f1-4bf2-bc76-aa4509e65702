"use strict";
/**
 * Utility functions for OpenAPI to MCP conversion
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidUrl = isValidUrl;
exports.validateOpenAPIVersion = validateOpenAPIVersion;
exports.extractBaseUrl = extractBaseUrl;
exports.generateOperationId = generateOperationId;
exports.convertOpenAPISchemaToJSONSchema = convertOpenAPISchemaToJSONSchema;
exports.sanitizeIdentifier = sanitizeIdentifier;
exports.toCamelCase = toCamelCase;
exports.generateSafeFilename = generateSafeFilename;
exports.ensureDir = ensureDir;
exports.fileExists = fileExists;
exports.convertPathToExpressRoute = convertPathToExpressRoute;
exports.extractPathParams = extractPathParams;
exports.formatBytes = formatBytes;
exports.deepMerge = deepMerge;
exports.generateId = generateId;
exports.isValidEmail = isValidEmail;
exports.sleep = sleep;
exports.retry = retry;
exports.getTimestamp = getTimestamp;
exports.capitalize = capitalize;
exports.toPascalCase = toPascalCase;
exports.isValidJSONSchema = isValidJSONSchema;
/**
 * Check if a string is a valid URL
 */
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    }
    catch {
        return false;
    }
}
/**
 * Validate OpenAPI version
 */
function validateOpenAPIVersion(version) {
    return /^3\.[01]\.\d+$/.test(version);
}
/**
 * Extract base URL from servers array
 */
function extractBaseUrl(servers) {
    if (!servers || servers.length === 0) {
        return 'http://localhost:3000';
    }
    let baseUrl = servers[0].url;
    // Handle server variables (basic replacement)
    baseUrl = baseUrl.replace(/\{[^}]+\}/g, '');
    // Ensure it's a valid URL
    try {
        new URL(baseUrl);
        return baseUrl;
    }
    catch {
        return 'http://localhost:3000';
    }
}
/**
 * Generate operation ID from path and method
 */
function generateOperationId(path, method) {
    // Remove path parameters and split by /
    const pathParts = path
        .split('/')
        .filter(part => part && !part.startsWith('{'))
        .map(part => part.charAt(0).toUpperCase() + part.slice(1));
    return method.toLowerCase() + pathParts.join('');
}
/**
 * Convert OpenAPI schema to JSON Schema
 */
function convertOpenAPISchemaToJSONSchema(schema) {
    const jsonSchema = {};
    // Copy basic properties
    if (schema.type)
        jsonSchema.type = schema.type;
    if (schema.title)
        jsonSchema.title = schema.title;
    if (schema.description)
        jsonSchema.description = schema.description;
    if (schema.default !== undefined)
        jsonSchema.default = schema.default;
    if (schema.example !== undefined)
        jsonSchema.examples = [schema.example];
    if (schema.enum)
        jsonSchema.enum = schema.enum;
    if (schema.format)
        jsonSchema.format = schema.format;
    // Handle object properties
    if (schema.properties) {
        jsonSchema.properties = {};
        for (const [key, value] of Object.entries(schema.properties)) {
            jsonSchema.properties[key] = convertOpenAPISchemaToJSONSchema(value);
        }
    }
    if (schema.required)
        jsonSchema.required = schema.required;
    if (schema.additionalProperties !== undefined) {
        if (typeof schema.additionalProperties === 'boolean') {
            jsonSchema.additionalProperties = schema.additionalProperties;
        }
        else {
            jsonSchema.additionalProperties = convertOpenAPISchemaToJSONSchema(schema.additionalProperties);
        }
    }
    // Handle array items
    if (schema.items) {
        jsonSchema.items = convertOpenAPISchemaToJSONSchema(schema.items);
    }
    // Handle composition keywords
    if (schema.allOf) {
        jsonSchema.allOf = schema.allOf.map(s => convertOpenAPISchemaToJSONSchema(s));
    }
    if (schema.oneOf) {
        jsonSchema.oneOf = schema.oneOf.map(s => convertOpenAPISchemaToJSONSchema(s));
    }
    if (schema.anyOf) {
        jsonSchema.anyOf = schema.anyOf.map(s => convertOpenAPISchemaToJSONSchema(s));
    }
    if (schema.not) {
        jsonSchema.not = convertOpenAPISchemaToJSONSchema(schema.not);
    }
    // Handle reference
    if (schema.$ref) {
        jsonSchema.$ref = schema.$ref;
    }
    return jsonSchema;
}
/**
 * Sanitize identifier for use as variable/function names
 */
function sanitizeIdentifier(input) {
    return input
        .replace(/[^a-zA-Z0-9_]/g, '_')
        .replace(/^[0-9]/, '_$&')
        .replace(/_+/g, '_')
        .replace(/^_+|_+$/g, '');
}
/**
 * Convert string to camelCase
 */
function toCamelCase(str) {
    return str
        .toLowerCase()
        .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase());
}
/**
 * Generate safe filename from string
 */
function generateSafeFilename(input) {
    return input
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+|-+$/g, '');
}
/**
 * Ensure directory exists
 */
async function ensureDir(dirPath) {
    const fs = await Promise.resolve().then(() => __importStar(require('fs-extra')));
    await fs.ensureDir(dirPath);
}
/**
 * Check if file exists
 */
async function fileExists(filePath) {
    try {
        const fs = await Promise.resolve().then(() => __importStar(require('fs-extra')));
        await fs.access(filePath);
        return true;
    }
    catch {
        return false;
    }
}
/**
 * Convert OpenAPI path to Express route format
 */
function convertPathToExpressRoute(path) {
    return path.replace(/{([^}]+)}/g, ':$1');
}
/**
 * Extract path parameters from OpenAPI path
 */
function extractPathParams(path) {
    const matches = path.match(/{([^}]+)}/g);
    if (!matches)
        return [];
    return matches.map(match => match.slice(1, -1));
}
/**
 * Format bytes to human readable string
 */
function formatBytes(bytes) {
    if (bytes === 0)
        return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
/**
 * Deep merge two objects
 */
function deepMerge(target, source) {
    const result = { ...target };
    for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = deepMerge(result[key] || {}, source[key]);
        }
        else {
            result[key] = source[key];
        }
    }
    return result;
}
/**
 * Generate random ID
 */
function generateId(length = 8) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}
/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
/**
 * Sleep for specified milliseconds
 */
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
/**
 * Retry function with exponential backoff
 */
async function retry(fn, maxAttempts = 3, baseDelay = 1000) {
    let lastError;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            return await fn();
        }
        catch (error) {
            lastError = error;
            if (attempt === maxAttempts) {
                throw lastError;
            }
            const delay = baseDelay * Math.pow(2, attempt - 1);
            await sleep(delay);
        }
    }
    throw lastError;
}
/**
 * Generate timestamp string
 */
function getTimestamp() {
    return new Date().toISOString().replace(/[:.]/g, '-').slice(0, -1);
}
/**
 * Capitalize first letter of string
 */
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
/**
 * Convert string to PascalCase
 */
function toPascalCase(str) {
    return capitalize(toCamelCase(str));
}
/**
 * Validate JSON Schema
 */
function isValidJSONSchema(schema) {
    if (!schema || typeof schema !== 'object')
        return false;
    // Basic validation - check for required properties
    const validTypes = ['string', 'number', 'integer', 'boolean', 'array', 'object', 'null'];
    if (schema.type && !validTypes.includes(schema.type)) {
        return false;
    }
    return true;
}
//# sourceMappingURL=utils.js.map