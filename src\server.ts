/**
 * API server entry point
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import * as path from 'path';
import axios from 'axios';
import { logProxyConfig, getAxiosConfigWithProxy } from './utils/proxy';
import { convertRoutes } from './routes/convert';
import { downloadRoutes } from './routes/download';
import { errorHandler } from './middleware/errorHandler';
import instantMcpRoutes from './routes/instantMcp';
import { serversRoutes } from './routes/servers';
import ServerManager from './services/serverManager';

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files (web UI) - check multiple locations
const publicPath = path.join(__dirname, '../public');
const uiDistPath = path.join(__dirname, '../ui/build');

// Try to serve UI build files first, then fallback to public
if (require('fs').existsSync(uiDistPath)) {
  app.use(express.static(uiDistPath));
} else if (require('fs').existsSync(publicPath)) {
  app.use(express.static(publicPath));
}

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime()
  });
});

// API routes
app.use('/api/convert', convertRoutes);
app.use('/api/download', downloadRoutes);
app.use('/api/instant-mcp', instantMcpRoutes);
app.use('/api/servers', serversRoutes);

// --- LLM Chat endpoint using LiteLLM Gateway ---
app.post('/api/chat', async (req, res) => {
  const { message } = req.body;
  if (!message) {
    return res.status(400).json({ error: 'No message provided' });
  }

  const llmUrl = process.env.LITELLM_URL || 'http://localhost:4000/chat/completions';
  const llmModel = process.env.LITELLM_MODEL || 'gpt-3.5-turbo';

  console.log('[API] LLM Chat request:', {
    url: llmUrl,
    model: llmModel,
    apiKeyPresent: !!process.env.LITELLM_API_KEY,
    message
  });

  try {
    // Call LiteLLM gateway with proxy support
    const axiosConfig = getAxiosConfigWithProxy(llmUrl);
    const llmRes = await axios.post(
      llmUrl,
      {
        model: llmModel,
        messages: [
          { role: 'system', content: 'You are an API assistant. Help the user interact with the API using natural language.' },
          { role: 'user', content: message }
        ]
      },
      axiosConfig
    );
    const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);

    res.json({ response: llmMessage });
  } catch (err: any) {
    console.error('[API] LLM call failed:', err && (err.response?.data || err.message || err.toString()), err);
    res.status(500).json({ error: 'LLM call failed', details: err && (err.response?.data || err.message || err.toString()) });
  }
});

// Root endpoint - serve web UI or API info
app.get('/', (req, res) => {
  if (req.accepts('html')) {
    // Serve HTML if available, otherwise API info
    res.sendFile(path.join(__dirname, '../public/index.html'), (err) => {
      if (err) {
        res.json({
          name: 'OpenAPI to MCP Converter',
          version: process.env.npm_package_version || '1.0.0',
          description: 'Convert OpenAPI specifications to MCP manifests and servers',
          endpoints: {
            '/api/health': 'GET - Health check',
            '/api/convert': 'POST - Convert OpenAPI to MCP',
            '/api/download/:bundleId': 'GET - Download generated bundle',
            '/api/chat': 'POST - Natural language chat with LLM'
          },
          documentation: 'https://github.com/yourusername/openapi-to-mcp'
        });
      }
    });
  } else {
    res.json({
      name: 'OpenAPI to MCP Converter',
      version: process.env.npm_package_version || '1.0.0',
      description: 'Convert OpenAPI specifications to MCP manifests and servers',
      endpoints: {
        '/api/health': 'GET - Health check',
        '/api/convert': 'POST - Convert OpenAPI to MCP',
        '/api/download/:bundleId': 'GET - Download generated bundle',
        '/api/chat': 'POST - Natural language chat with LLM'
      },
      documentation: 'https://github.com/yourusername/openapi-to-mcp'
    });
  }
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Serve index.html for all non-API routes (SPA fallback)
app.get('*', (req, res) => {
  if (!req.path.startsWith('/api/')) {
    res.sendFile(path.join(__dirname, '../public/index.html'));
  } else {
    res.status(404).json({
      error: 'Not Found',
      message: `Route ${req.originalUrl} not found`,
      availableEndpoints: [
        'GET /',
        'GET /api/health',
        'POST /api/convert',
        'GET /api/download/:bundleId',
        'POST /api/chat'
      ]
    });
  }
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 OpenAPI to MCP Server running on port ${PORT}`);
  console.log(`📖 API Documentation: http://localhost:${PORT}/`);
  console.log(`🔧 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`💬 Chat endpoint: http://localhost:${PORT}/api/chat`);

  // Log proxy configuration
  logProxyConfig();

  if (process.env.NODE_ENV === 'development') {
    console.log(`📱 Web UI: http://localhost:${PORT}/`);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export default app;
