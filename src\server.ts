/**
 * API server entry point
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import * as path from 'path';
// axios removed - no longer needed for LLM calls
import { convertRoutes } from './routes/convert';
import { downloadRoutes } from './routes/download';
import { errorHandler } from './middleware/errorHandler';
import instantMcpRoutes from './routes/instantMcp';
import { serversRoutes } from './routes/servers';
import ServerManager from './services/serverManager';

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());
app.use(cors({
  origin: process.env.CORS_ORIGIN || '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// No static file serving - this is a pure API server for MCP generation

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    uptime: process.uptime()
  });
});

// API routes
app.use('/api/convert', convertRoutes);
app.use('/api/download', downloadRoutes);
app.use('/api/instant-mcp', instantMcpRoutes);
app.use('/api/servers', serversRoutes);

// No chat endpoint - this is a pure MCP server generator

// Root endpoint - API info only
app.get('/', (req, res) => {
  res.json({
    name: 'OpenAPI to MCP Server Generator',
    version: process.env.npm_package_version || '1.0.0',
    description: 'Convert OpenAPI specifications to MCP servers',
    endpoints: {
      '/api/health': 'GET - Health check',
      '/api/convert': 'POST - Convert OpenAPI to MCP',
      '/api/download/:bundleId': 'GET - Download generated bundle'
    },
    documentation: 'https://github.com/yourusername/openapi-to-mcp'
  });
});

// Error handling middleware (must be last)
app.use(errorHandler);

// Serve index.html for all non-API routes (SPA fallback)
app.get('*', (req, res) => {
  if (!req.path.startsWith('/api/')) {
    res.sendFile(path.join(__dirname, '../public/index.html'));
  } else {
    res.status(404).json({
      error: 'Not Found',
      message: `Route ${req.originalUrl} not found`,
      availableEndpoints: [
        'GET /',
        'GET /api/health',
        'POST /api/convert',
        'GET /api/download/:bundleId',
        // No chat endpoint
      ]
    });
  }
});

// Start server
const server = app.listen(PORT, () => {
  console.log(`🚀 OpenAPI to MCP Server running on port ${PORT}`);
  console.log(`📖 API Documentation: http://localhost:${PORT}/`);
  console.log(`🔧 Health Check: http://localhost:${PORT}/api/health`);
  // Pure MCP server generator - no chat or UI endpoints
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

export default app;
