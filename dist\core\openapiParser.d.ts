/**
 * OpenAPI specification parser
 */
import { OpenAPISpec, ParsedOpenAPI, ValidationResult } from '../types';
export declare class OpenAPIParser {
    /**
     * Parse OpenAPI specification from file
     */
    parseFromFile(filePath: string): Promise<ParsedOpenAPI>;
    /**
     * Parse OpenAPI specification from URL
     */
    parseFromURL(url: string): Promise<ParsedOpenAPI>;
    /**
     * Parse OpenAPI specification from string content
     */
    parseFromString(content: string, isYaml?: boolean): Promise<ParsedOpenAPI>;
    /**
     * Parse OpenAPI specification object
     */
    parseSpec(spec: OpenAPISpec): ParsedOpenAPI;
    /**
     * Validate OpenAPI specification
     */
    validateSpec(spec: any): ValidationResult;
    /**
     * Parse all endpoints from OpenAPI paths
     */
    private parseEndpoints;
    /**
     * Parse operation parameters
     */
    private parseParameters;
    /**
     * Parse request body
     */
    private parseRequestBody;
    /**
     * Parse operation responses
     */
    private parseResponses;
    /**
     * Extract all tags from the specification
     */
    extractTags(spec: OpenAPISpec): string[];
    /**
     * Get statistics about the OpenAPI specification
     */
    getSpecStats(parsed: ParsedOpenAPI): {
        endpoints: number;
        methods: Record<string, number>;
        tags: string[];
        hasAuthentication: boolean;
        hasRequestBodies: number;
        hasParameters: number;
    };
}
export default OpenAPIParser;
//# sourceMappingURL=openapiParser.d.ts.map