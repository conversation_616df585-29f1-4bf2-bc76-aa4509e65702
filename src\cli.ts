/**
 * CLI entry point for OpenAPI to MCP converter
 */

import { Command } from 'commander';
import * as fs from 'fs-extra';
import * as path from 'path';
import chalk from 'chalk';
import ora from 'ora';
import { OpenAPIParser } from './core/openapiParser';
import { MCPManifestGenerator } from './core/mcpManifestGenerator';
import { ServerGenerator } from './core/serverGenerator';
import { BundleGenerator } from './core/bundleGenerator';
import { CLIOptions, ServerConfig } from './types';
import { 
  isValidUrl, 
  generateSafeFilename, 
  ensureDir,
  fileExists
} from './utils';

const program = new Command();

// CLI Configuration
program
  .name('openapi-to-mcp')
  .description('Convert OpenAPI specifications to MCP manifests and servers')
  .version('1.0.0');

// Convert command (default)
program
  .argument('<input>', 'OpenAPI specification file or URL')
  .option('-o, --output <path>', 'Output directory', './mcp-server')
  .option('-n, --name <name>', 'Server name')
  .option('-v, --version <version>', 'Server version', '1.0.0')
  .option('-p, --port <port>', 'Server port', '8000')
  .option('-b, --base-url <url>', 'Base API URL')
  .option('-a, --author <author>', 'Author name')
  .option('-l, --license <license>', 'License', 'MIT')
  .option('--verbose', 'Verbose output', false)
  .option('--dry-run', 'Show what would be generated without creating files', false)
  .action(async (input: string, options: any) => {
    await convertCommand(input, options);
  });

// Serve command
program
  .command('serve')
  .description('Start the API server')
  .option('-p, --port <port>', 'Server port', '3000')
  .option('--cors-origin <origin>', 'CORS origin', '*')
  .action(async (options: any) => {
    await serveCommand(options);
  });

// Info command
program
  .command('info <input>')
  .description('Show information about an OpenAPI specification')
  .action(async (input: string) => {
    await infoCommand(input);
  });

// Validate command
program
  .command('validate <input>')
  .description('Validate an OpenAPI specification')
  .action(async (input: string) => {
    await validateCommand(input);
  });

/**
 * Main convert command implementation
 */
async function convertCommand(input: string, options: CLIOptions): Promise<void> {
  const spinner = ora('Initializing conversion...').start();
  
  try {
    // Parse input
    spinner.text = 'Parsing OpenAPI specification...';
    const parser = new OpenAPIParser();
    let parsed;

    if (isValidUrl(input)) {
      parsed = await parser.parseFromURL(input);
    } else if (await fileExists(input)) {
      parsed = await parser.parseFromFile(input);
    } else {
      throw new Error(`Input file not found: ${input}`);
    }

    spinner.succeed('OpenAPI specification parsed successfully');

    // Create server configuration
    const config: ServerConfig = {
      name: options.name || generateSafeFilename(parsed.spec.info.title) || 'mcp-server',
      version: options.version || parsed.spec.info.version || '1.0.0',
      description: parsed.spec.info.description || 'Generated MCP server',
      port: options.port ? parseInt(options.port) : 8000,
      baseUrl: options.baseUrl || parsed.baseUrl,
      outputDir: path.resolve(options.output || './mcp-server'),
      author: options.author,
      license: options.license || 'MIT'
    };

    if (options.verbose) {
      console.log(chalk.blue('\nConfiguration:'));
      console.log(JSON.stringify(config, null, 2));
    }

    // Generate MCP manifest
    spinner.start('Generating MCP manifest...');
    const manifestGenerator = new MCPManifestGenerator();
    const manifest = manifestGenerator.generateManifest(parsed, config);
    spinner.succeed(`Generated ${manifest.tools.length} MCP tools`);

    // Generate server code
    spinner.start('Generating server code...');
    const serverGenerator = new ServerGenerator();
    const generatedFiles = await serverGenerator.generateServer(parsed, config);
    spinner.succeed('Server code generated');

    if (options.dryRun) {
      // Dry run - show what would be generated
      console.log(chalk.yellow('\n🔍 Dry run - showing what would be generated:\n'));
      
      console.log(chalk.blue('Files that would be created:'));
      for (const filePath of Object.keys(generatedFiles)) {
        console.log(chalk.gray(`  ${path.join(config.outputDir, filePath)}`));
      }
      
      console.log(chalk.blue('\nMCP Manifest:'));
      console.log(manifestGenerator.generateSummary(manifest));
      
      console.log(chalk.green('\n✅ Dry run completed'));
      return;
    }

    // Write files to disk
    spinner.start('Writing files...');
    await ensureDir(config.outputDir);

    // Write MCP manifest
    await fs.writeFile(
      path.join(config.outputDir, 'mcp.json'),
      JSON.stringify(manifest, null, 2)
    );

    // Write all generated files
    for (const [filePath, content] of Object.entries(generatedFiles)) {
      const fullPath = path.join(config.outputDir, filePath);
      await ensureDir(path.dirname(fullPath));
      await fs.writeFile(fullPath, content);
    }

    spinner.succeed('Files written successfully');

    // Success message
    console.log(chalk.green('\n🎉 Conversion completed successfully!\n'));
    
    console.log(chalk.blue('Generated files:'));
    console.log(chalk.gray(`📁 ${config.outputDir}/`));
    console.log(chalk.gray(`├── mcp.json`));
    for (const filePath of Object.keys(generatedFiles)) {
      const parts = filePath.split('/');
      const indent = '├── ' + '  '.repeat(parts.length - 1);
      console.log(chalk.gray(`${indent}${parts[parts.length - 1]}`));
    }

    console.log(chalk.blue('\nNext steps:'));
    console.log(chalk.white(`1. cd ${config.outputDir}`));
    console.log(chalk.white('2. npm install'));
    console.log(chalk.white('3. npm run build'));
    console.log(chalk.white('4. npm start'));

    console.log(chalk.blue('\nServer info:'));
    console.log(chalk.white(`• Name: ${config.name}`));
    console.log(chalk.white(`• Version: ${config.version}`));
    console.log(chalk.white(`• Port: ${config.port}`));
    console.log(chalk.white(`• Tools: ${manifest.tools.length}`));
    console.log(chalk.white(`• Base URL: ${config.baseUrl}`));

  } catch (error) {
    spinner.fail('Conversion failed');
    console.error(chalk.red('\n❌ Error:'), (error as Error).message);
    if (options.verbose && error instanceof Error) {
      console.error(chalk.gray(error.stack));
    }
    process.exit(1);
  }
}

/**
 * Serve command implementation
 */
async function serveCommand(options: any): Promise<void> {
  const port = parseInt(options.port) || 3000;
  
  console.log(chalk.blue('🚀 Starting OpenAPI-to-MCP API server...\n'));
  
  // Set environment variables
  process.env.PORT = port.toString();
  process.env.CORS_ORIGIN = options.corsOrigin || '*';
  
  try {
    // Import and start the server
    const { default: app } = await import('./server');
    
    console.log(chalk.green('✅ Server started successfully!'));
    console.log(chalk.blue('\nAvailable endpoints:'));
    console.log(chalk.white(`• GET  http://localhost:${port}/`));
    console.log(chalk.white(`• GET  http://localhost:${port}/api/health`));
    console.log(chalk.white(`• POST http://localhost:${port}/api/convert`));
    console.log(chalk.white(`• POST http://localhost:${port}/api/convert/upload`));
    console.log(chalk.white(`• GET  http://localhost:${port}/api/download/:bundleId`));
    
  } catch (error) {
    console.error(chalk.red('❌ Failed to start server:'), (error as Error).message);
    process.exit(1);
  }
}

/**
 * Info command implementation
 */
async function infoCommand(input: string): Promise<void> {
  const spinner = ora('Loading OpenAPI specification...').start();
  
  try {
    const parser = new OpenAPIParser();
    let parsed;

    if (isValidUrl(input)) {
      parsed = await parser.parseFromURL(input);
    } else if (await fileExists(input)) {
      parsed = await parser.parseFromFile(input);
    } else {
      throw new Error(`Input file not found: ${input}`);
    }

    spinner.succeed('OpenAPI specification loaded');

    // Display information
    const spec = parsed.spec;
    const stats = parser.getSpecStats(parsed);

    console.log(chalk.blue('\n📋 OpenAPI Specification Info\n'));
    
    console.log(chalk.white('Basic Information:'));
    console.log(chalk.gray(`• Title: ${spec.info.title}`));
    console.log(chalk.gray(`• Version: ${spec.info.version}`));
    console.log(chalk.gray(`• OpenAPI Version: ${spec.openapi}`));
    if (spec.info.description) {
      console.log(chalk.gray(`• Description: ${spec.info.description}`));
    }

    console.log(chalk.white('\nServers:'));
    if (spec.servers && spec.servers.length > 0) {
      for (const server of spec.servers) {
        console.log(chalk.gray(`• ${server.url}${server.description ? ` - ${server.description}` : ''}`));
      }
    } else {
      console.log(chalk.gray('• No servers defined'));
    }

    console.log(chalk.white('\nEndpoints:'));
    console.log(chalk.gray(`• Total: ${stats.endpoints}`));
    for (const [method, count] of Object.entries(stats.methods)) {
      console.log(chalk.gray(`• ${method.toUpperCase()}: ${count}`));
    }

    if (stats.tags.length > 0) {
      console.log(chalk.white('\nTags:'));
      for (const tag of stats.tags) {
        console.log(chalk.gray(`• ${tag}`));
      }
    }

    console.log(chalk.white('\nFeatures:'));
    console.log(chalk.gray(`• Has Authentication: ${stats.hasAuthentication ? 'Yes' : 'No'}`));
    console.log(chalk.gray(`• Endpoints with Request Bodies: ${stats.hasRequestBodies}`));
    console.log(chalk.gray(`• Endpoints with Parameters: ${stats.hasParameters}`));

    // Show what would be generated
    const manifestGenerator = new MCPManifestGenerator();
    const tools = manifestGenerator.generateTools(parsed);
    const toolStats = manifestGenerator.getToolStats(tools);

    console.log(chalk.blue('\n🛠️  MCP Generation Preview\n'));
    console.log(chalk.white('Would generate:'));
    console.log(chalk.gray(`• ${toolStats.total} MCP tools`));
    console.log(chalk.gray(`• ${toolStats.withOutput} tools with output schemas`));
    console.log(chalk.gray(`• ${toolStats.withRequiredInput} tools with required input`));
    console.log(chalk.gray(`• ${toolStats.averageInputProperties} average input properties per tool`));

  } catch (error) {
    spinner.fail('Failed to load specification');
    console.error(chalk.red('\n❌ Error:'), (error as Error).message);
    process.exit(1);
  }
}

/**
 * Validate command implementation
 */
async function validateCommand(input: string): Promise<void> {
  const spinner = ora('Validating OpenAPI specification...').start();
  
  try {
    const parser = new OpenAPIParser();
    let content: string;

    if (isValidUrl(input)) {
      spinner.text = 'Fetching specification from URL...';
      const axios = await import('axios');
      const response = await axios.default.get(input);
      content = typeof response.data === 'string' ? response.data : JSON.stringify(response.data);
    } else if (await fileExists(input)) {
      content = await fs.readFile(input, 'utf-8');
    } else {
      throw new Error(`Input file not found: ${input}`);
    }

    // Parse the specification
    const isYaml = input.endsWith('.yaml') || input.endsWith('.yml') || content.trim().startsWith('openapi:');
    const spec = isYaml ? 
      (await import('js-yaml')).load(content) : 
      JSON.parse(content);

    // Validate
    const validation = parser.validateSpec(spec);

    if (validation.valid) {
      spinner.succeed('OpenAPI specification is valid');
      
      if (validation.warnings.length > 0) {
        console.log(chalk.yellow('\n⚠️  Warnings:'));
        for (const warning of validation.warnings) {
          console.log(chalk.yellow(`• ${warning}`));
        }
      }
      
      console.log(chalk.green('\n✅ Validation passed!'));
    } else {
      spinner.fail('OpenAPI specification is invalid');
      
      console.log(chalk.red('\n❌ Validation errors:'));
      for (const error of validation.errors) {
        console.log(chalk.red(`• ${error}`));
      }
      
      if (validation.warnings.length > 0) {
        console.log(chalk.yellow('\n⚠️  Warnings:'));
        for (const warning of validation.warnings) {
          console.log(chalk.yellow(`• ${warning}`));
        }
      }
      
      process.exit(1);
    }

  } catch (error) {
    spinner.fail('Validation failed');
    console.error(chalk.red('\n❌ Error:'), (error as Error).message);
    process.exit(1);
  }
}

// Parse command line arguments
program.parse();
