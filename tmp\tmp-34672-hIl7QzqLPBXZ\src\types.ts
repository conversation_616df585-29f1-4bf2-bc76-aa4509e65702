
// Generated types for MCP server

export interface ToolRequest {
  [key: string]: any;
}

export interface ToolResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
  status?: number;
}

export interface HealthResponse {
  status: string;
  name: string;
  version: string;
  timestamp: string;
  baseUrl: string;
}

export interface MCPManifest {
  // ...other fields...
  tags?: any[];
}

// OpenAPI tool definitions for LLM function calling
export const openApiTools = [
  {
    "type": "function",
    "function": {
      "name": "addPet",
      "description": "Add a new pet to the store.",
      "parameters": {
        "type": "object",
        "properties": {
          "body": {
            "type": "object",
            "description": "Create a new pet in the store"
          }
        },
        "required": [
          "body"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "updatePet",
      "description": "Update an existing pet.",
      "parameters": {
        "type": "object",
        "properties": {
          "body": {
            "type": "object",
            "description": "Update an existent pet in the store"
          }
        },
        "required": [
          "body"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "findPetsByStatus",
      "description": "Finds Pets by status. Use this to find, list, search, or get pets by their status (available, pending, sold).",
      "parameters": {
        "type": "object",
        "properties": {
          "query": {
            "type": "object",
            "description": "Query parameters",
            "properties": {
              "status": {
                "type": "string",
                "description": "Status values that need to be considered for filter"
              }
            }
          }
        },
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "findPetsByTags",
      "description": "Finds Pets by tags.",
      "parameters": {
        "type": "object",
        "properties": {
          "query": {
            "type": "object",
            "description": "Query parameters",
            "properties": {
              "tags": {
                "type": "array",
                "description": "Tags to filter by"
              }
            }
          }
        },
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "getPetById",
      "description": "Find pet by ID.",
      "parameters": {
        "type": "object",
        "properties": {
          "petId": {
            "type": "integer",
            "description": "ID of pet to return"
          }
        },
        "required": [
          "petId"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "updatePetWithForm",
      "description": "Updates a pet in the store with form data.",
      "parameters": {
        "type": "object",
        "properties": {
          "petId": {
            "type": "integer",
            "description": "ID of pet that needs to be updated"
          },
          "query": {
            "type": "object",
            "description": "Query parameters",
            "properties": {
              "name": {
                "type": "string",
                "description": "Name of pet that needs to be updated"
              },
              "status": {
                "type": "string",
                "description": "Status of pet that needs to be updated"
              }
            }
          }
        },
        "required": [
          "petId"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "deletePet",
      "description": "Deletes a pet.",
      "parameters": {
        "type": "object",
        "properties": {
          "petId": {
            "type": "integer",
            "description": "Pet id to delete"
          },
          "headers": {
            "type": "object",
            "description": "HTTP headers",
            "properties": {
              "api_key": {
                "type": "string",
                "description": "Header: api_key"
              }
            }
          }
        },
        "required": [
          "petId"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "uploadFile",
      "description": "Uploads an image.",
      "parameters": {
        "type": "object",
        "properties": {
          "petId": {
            "type": "integer",
            "description": "ID of pet to update"
          },
          "query": {
            "type": "object",
            "description": "Query parameters",
            "properties": {
              "additionalMetadata": {
                "type": "string",
                "description": "Additional Metadata"
              }
            }
          },
          "body": {
            "type": "object",
            "description": "Request body"
          }
        },
        "required": [
          "petId"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "getInventory",
      "description": "Returns pet inventories by status.",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "placeOrder",
      "description": "Place an order for a pet.",
      "parameters": {
        "type": "object",
        "properties": {
          "body": {
            "type": "object",
            "description": "Request body"
          }
        },
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "getOrderById",
      "description": "Find purchase order by ID.",
      "parameters": {
        "type": "object",
        "properties": {
          "orderId": {
            "type": "integer",
            "description": "ID of order that needs to be fetched"
          }
        },
        "required": [
          "orderId"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "deleteOrder",
      "description": "Delete purchase order by identifier.",
      "parameters": {
        "type": "object",
        "properties": {
          "orderId": {
            "type": "integer",
            "description": "ID of the order that needs to be deleted"
          }
        },
        "required": [
          "orderId"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "createUser",
      "description": "Create user.",
      "parameters": {
        "type": "object",
        "properties": {
          "body": {
            "type": "object",
            "description": "Created user object"
          }
        },
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "createUsersWithListInput",
      "description": "Creates list of users with given input array.",
      "parameters": {
        "type": "object",
        "properties": {
          "body": {
            "type": "object",
            "description": "Request body"
          }
        },
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "loginUser",
      "description": "Logs user into the system.",
      "parameters": {
        "type": "object",
        "properties": {
          "query": {
            "type": "object",
            "description": "Query parameters",
            "properties": {
              "username": {
                "type": "string",
                "description": "The user name for login"
              },
              "password": {
                "type": "string",
                "description": "The password for login in clear text"
              }
            }
          }
        },
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "logoutUser",
      "description": "Logs out current logged in user session.",
      "parameters": {
        "type": "object",
        "properties": {},
        "required": []
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "getUserByName",
      "description": "Get user by user name.",
      "parameters": {
        "type": "object",
        "properties": {
          "username": {
            "type": "string",
            "description": "The name that needs to be fetched. Use user1 for testing"
          }
        },
        "required": [
          "username"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "updateUser",
      "description": "Update user resource.",
      "parameters": {
        "type": "object",
        "properties": {
          "username": {
            "type": "string",
            "description": "name that need to be deleted"
          },
          "body": {
            "type": "object",
            "description": "Update an existent user in the store"
          }
        },
        "required": [
          "username"
        ]
      }
    }
  },
  {
    "type": "function",
    "function": {
      "name": "deleteUser",
      "description": "Delete user resource.",
      "parameters": {
        "type": "object",
        "properties": {
          "username": {
            "type": "string",
            "description": "The name that needs to be deleted"
          }
        },
        "required": [
          "username"
        ]
      }
    }
  }
];

// OpenAPI derived types
export interface addPetRequest {
  body: any;
  tags?: any[];
}

export interface updatePetRequest {
  body: any;
  tags?: any[];
}

export interface findPetsByStatusRequest {
  query?: {
    status?: string;
  };
  tags?: any[];
}

export interface findPetsByTagsRequest {
  query?: {
    tags?: any[];
  };
  tags?: any[];
}

export interface getPetByIdRequest {
  petId: string;
  tags?: any[];
}

export interface updatePetWithFormRequest {
  petId: string;
  query?: {
    name?: string;
    status?: string;
  };
  tags?: any[];
}

export interface deletePetRequest {
  petId: string;
  headers?: {
    api_key?: string;
  };
  tags?: any[];
}

export interface uploadFileRequest {
  petId: string;
  query?: {
    additionalMetadata?: string;
  };
  body?: any;
  tags?: any[];
}

export interface getInventoryRequest {
  tags?: any[];
}

export interface placeOrderRequest {
  body?: any;
  tags?: any[];
}

export interface getOrderByIdRequest {
  orderId: string;
  tags?: any[];
}

export interface deleteOrderRequest {
  orderId: string;
  tags?: any[];
}

export interface createUserRequest {
  body?: any;
  tags?: any[];
}

export interface createUsersWithListInputRequest {
  body?: any;
  tags?: any[];
}

export interface loginUserRequest {
  query?: {
    username?: string;
    password?: string;
  };
  tags?: any[];
}

export interface logoutUserRequest {
  tags?: any[];
}

export interface getUserByNameRequest {
  username: string;
  tags?: any[];
}

export interface updateUserRequest {
  username: string;
  body?: any;
  tags?: any[];
}

export interface deleteUserRequest {
  username: string;
  tags?: any[];
}
