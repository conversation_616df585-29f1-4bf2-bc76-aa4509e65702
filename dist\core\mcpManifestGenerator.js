"use strict";
/**
 * MCP manifest generator
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPManifestGenerator = void 0;
const types_1 = require("../types");
const utils_1 = require("../utils");
class MCPManifestGenerator {
    /**
     * Generate MCP tools from parsed OpenAPI
     */
    generateTools(parsed) {
        const tools = [];
        for (const endpoint of parsed.endpoints) {
            const tool = this.createToolFromEndpoint(endpoint);
            tools.push(tool);
        }
        return tools;
    }
    /**
     * Generate complete MCP manifest
     */
    generateManifest(parsed, config) {
        try {
            const tools = this.generateTools(parsed);
            const manifest = {
                name: config.name,
                version: config.version,
                description: config.description,
                author: config.author,
                license: config.license,
                tools,
                server: {
                    command: 'node',
                    args: ['dist/server.js'],
                    env: {
                        PORT: config.port.toString(),
                        BASE_URL: config.baseUrl
                    }
                }
            };
            return manifest;
        }
        catch (error) {
            throw new types_1.MCPGenerationError('Failed to generate MCP manifest', error);
        }
    }
    /**
     * Create MCP tool from OpenAPI endpoint
     */
    createToolFromEndpoint(endpoint) {
        const toolName = this.generateToolName(endpoint);
        const description = this.generateToolDescription(endpoint);
        const inputSchema = this.generateInputSchema(endpoint);
        const outputSchema = this.generateOutputSchema(endpoint);
        return {
            name: toolName,
            description,
            inputSchema,
            outputSchema
        };
    }
    /**
     * Generate tool name from endpoint
     */
    generateToolName(endpoint) {
        // Use operationId if available, otherwise generate from path and method
        let name = endpoint.operationId;
        if (!name) {
            // Generate name from method and path
            const pathParts = endpoint.path
                .split('/')
                .filter(part => part && !part.startsWith('{'))
                .map(part => (0, utils_1.toCamelCase)(part));
            name = endpoint.method + pathParts.map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('');
        }
        return (0, utils_1.sanitizeIdentifier)(name);
    }
    /**
     * Generate tool description
     */
    generateToolDescription(endpoint) {
        if (endpoint.summary) {
            return endpoint.summary;
        }
        if (endpoint.description) {
            return endpoint.description;
        }
        // Generate description from method and path
        const method = endpoint.method.toUpperCase();
        const path = endpoint.path;
        return `${method} ${path}`;
    }
    /**
     * Generate input schema for tool
     */
    generateInputSchema(endpoint) {
        const properties = {};
        const required = [];
        // Add path parameters
        const pathParams = endpoint.parameters.filter(p => p.in === 'path');
        for (const param of pathParams) {
            properties[param.name] = {
                ...param.schema,
                description: param.description
            };
            if (param.required) {
                required.push(param.name);
            }
        }
        // Add query parameters
        const queryParams = endpoint.parameters.filter(p => p.in === 'query');
        if (queryParams.length > 0) {
            const queryProperties = {};
            const queryRequired = [];
            for (const param of queryParams) {
                queryProperties[param.name] = {
                    ...param.schema,
                    description: param.description
                };
                if (param.required) {
                    queryRequired.push(param.name);
                }
            }
            properties.query = {
                type: 'object',
                properties: queryProperties,
                required: queryRequired.length > 0 ? queryRequired : undefined,
                description: 'Query parameters'
            };
        }
        // Add header parameters
        const headerParams = endpoint.parameters.filter(p => p.in === 'header');
        if (headerParams.length > 0) {
            const headerProperties = {};
            const headerRequired = [];
            for (const param of headerParams) {
                headerProperties[param.name] = {
                    ...param.schema,
                    description: param.description
                };
                if (param.required) {
                    headerRequired.push(param.name);
                }
            }
            properties.headers = {
                type: 'object',
                properties: headerProperties,
                required: headerRequired.length > 0 ? headerRequired : undefined,
                description: 'Request headers'
            };
        }
        // Add request body
        if (endpoint.requestBody) {
            properties.body = {
                ...endpoint.requestBody.schema,
                description: endpoint.requestBody.description || 'Request body'
            };
            if (endpoint.requestBody.required) {
                required.push('body');
            }
        }
        // If no properties, create a simple object
        if (Object.keys(properties).length === 0) {
            return {
                type: 'object',
                properties: {},
                description: 'No input parameters required'
            };
        }
        return {
            type: 'object',
            properties,
            required: required.length > 0 ? required : undefined,
            additionalProperties: false
        };
    }
    /**
     * Generate output schema for tool
     */
    generateOutputSchema(endpoint) {
        // Find successful response (2xx status codes)
        const successResponse = endpoint.responses.find(r => r.status.startsWith('2') || r.status === 'default');
        if (!successResponse) {
            return undefined;
        }
        if (successResponse.schema) {
            return {
                ...successResponse.schema,
                description: successResponse.description
            };
        }
        // If no schema but has content type, create basic response
        if (successResponse.contentType) {
            if (successResponse.contentType.includes('json')) {
                return {
                    type: 'object',
                    description: successResponse.description
                };
            }
            else if (successResponse.contentType.includes('text')) {
                return {
                    type: 'string',
                    description: successResponse.description
                };
            }
        }
        return {
            type: 'object',
            description: successResponse.description
        };
    }
    /**
     * Validate generated manifest
     */
    validateManifest(manifest) {
        const errors = [];
        // Validate basic fields
        if (!manifest.name) {
            errors.push('Manifest must have a name');
        }
        if (!manifest.version) {
            errors.push('Manifest must have a version');
        }
        if (!manifest.description) {
            errors.push('Manifest must have a description');
        }
        if (!manifest.tools || !Array.isArray(manifest.tools)) {
            errors.push('Manifest must have a tools array');
        }
        else if (manifest.tools.length === 0) {
            errors.push('Manifest must have at least one tool');
        }
        if (!manifest.server) {
            errors.push('Manifest must have a server configuration');
        }
        else {
            if (!manifest.server.command) {
                errors.push('Server must have a command');
            }
        }
        // Validate tools
        for (let i = 0; i < manifest.tools.length; i++) {
            const tool = manifest.tools[i];
            const toolErrors = this.validateTool(tool, i);
            errors.push(...toolErrors);
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }
    /**
     * Validate individual tool
     */
    validateTool(tool, index) {
        const errors = [];
        const prefix = `Tool ${index}`;
        if (!tool.name) {
            errors.push(`${prefix}: must have a name`);
        }
        if (!tool.description) {
            errors.push(`${prefix}: must have a description`);
        }
        if (!tool.inputSchema) {
            errors.push(`${prefix}: must have an input schema`);
        }
        else if (typeof tool.inputSchema !== 'object') {
            errors.push(`${prefix}: input schema must be an object`);
        }
        return errors;
    }
    /**
     * Generate tool statistics
     */
    getToolStats(tools) {
        const stats = {
            total: tools.length,
            withOutput: 0,
            withRequiredInput: 0,
            averageInputProperties: 0
        };
        let totalInputProperties = 0;
        for (const tool of tools) {
            if (tool.outputSchema) {
                stats.withOutput++;
            }
            if (tool.inputSchema.required && tool.inputSchema.required.length > 0) {
                stats.withRequiredInput++;
            }
            if (tool.inputSchema.properties) {
                totalInputProperties += Object.keys(tool.inputSchema.properties).length;
            }
        }
        stats.averageInputProperties = tools.length > 0
            ? Math.round((totalInputProperties / tools.length) * 100) / 100
            : 0;
        return stats;
    }
    /**
     * Generate manifest summary
     */
    generateSummary(manifest) {
        const toolStats = this.getToolStats(manifest.tools);
        return `
MCP Manifest Summary:
- Name: ${manifest.name}
- Version: ${manifest.version}
- Tools: ${toolStats.total}
- Tools with output schemas: ${toolStats.withOutput}
- Tools with required input: ${toolStats.withRequiredInput}
- Average input properties per tool: ${toolStats.averageInputProperties}
    `.trim();
    }
}
exports.MCPManifestGenerator = MCPManifestGenerator;
// Default export for convenience
exports.default = MCPManifestGenerator;
//# sourceMappingURL=mcpManifestGenerator.js.map