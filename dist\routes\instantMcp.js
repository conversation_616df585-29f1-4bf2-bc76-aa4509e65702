"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const tmp_promise_1 = __importDefault(require("tmp-promise"));
const fs_extra_1 = __importDefault(require("fs-extra"));
const path_1 = __importDefault(require("path"));
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
const portfinder_1 = __importDefault(require("portfinder"));
const child_process_1 = require("child_process");
const serverGenerator_1 = __importDefault(require("../core/serverGenerator"));
const openapiParser_1 = __importDefault(require("../core/openapiParser"));
const router = express_1.default.Router();
// In-memory registry of running MCP servers
const mcpInstances = {};
router.post('/', async (req, res) => {
    const { openapiUrl } = req.body;
    if (!openapiUrl)
        return res.status(400).json({ error: 'Missing openapiUrl' });
    try {
        // Ensure project-local tmp directory exists
        await fs_extra_1.default.ensureDir(path_1.default.join(process.cwd(), 'tmp'));
        // 1. Generate MCP server code in a temp dir (project-local to avoid Windows spawn EINVAL)
        const tmpDir = await tmp_promise_1.default.dir({ unsafeCleanup: true, tmpdir: path_1.default.join(process.cwd(), 'tmp') });
        const mcpDir = tmpDir.path;
        // 2. Parse OpenAPI and generate MCP server files
        const openapiRaw = await fetch(openapiUrl).then(r => r.text());
        const parser = new openapiParser_1.default();
        const isYaml = openapiUrl.endsWith('.yaml') || openapiUrl.endsWith('.yml');
        const parsed = await parser.parseFromString(openapiRaw, isYaml);
        const port = await portfinder_1.default.getPortPromise();
        const config = {
            name: 'instant-mcp',
            version: '1.0.0',
            port,
            baseUrl: `http://localhost:${port}`,
            description: parsed.spec.info?.description || 'Instant MCP server',
            license: parsed.spec.info?.license?.name || 'MIT',
            author: parsed.spec.info?.contact?.name || 'openapi-to-mcp',
            outputDir: mcpDir
        };
        const serverGen = new serverGenerator_1.default();
        const files = await serverGen.generateServer(parsed, config);
        for (const [relPath, content] of Object.entries(files)) {
            await fs_extra_1.default.outputFile(path_1.default.join(mcpDir, relPath), String(content));
        }
        // Copy .env.example to .env so the MCP server has environment variables
        await fs_extra_1.default.copyFile(path_1.default.join(mcpDir, '.env.example'), path_1.default.join(mcpDir, '.env'));
        // Cross-platform npm command
        const npmCmd = process.platform === 'win32' ? 'npm.cmd' : 'npm';
        // Debug/logging for spawn EINVAL
        if (!fs_extra_1.default.existsSync(mcpDir)) {
            throw new Error(`Temp directory does not exist: ${mcpDir}`);
        }
        const filesInDir = fs_extra_1.default.readdirSync(mcpDir);
        console.log('npmCmd:', npmCmd, 'cwd:', mcpDir, 'files:', filesInDir);
        // Extra: check if npmCmd is absolute or in PATH
        const which = require('which');
        try {
            const npmPath = which.sync(npmCmd);
            console.log('Resolved npm path:', npmPath);
        }
        catch (e) {
            console.error('npmCmd not found in PATH:', npmCmd);
        }
        // 3. Install dependencies
        try {
            await runCmd(npmCmd, ['install'], { cwd: mcpDir });
        }
        catch (err) {
            console.error('npm install failed:', err);
            throw err;
        }
        // 4. Build
        try {
            await runCmd(npmCmd, ['run', 'build'], { cwd: mcpDir });
        }
        catch (err) {
            console.error('npm run build failed:', err);
            throw err;
        }
        // Do NOT start the MCP server automatically.
        // Instead, return the path and instructions for manual start.
        const instanceId = path_1.default.basename(String(mcpDir));
        res.json({
            instanceId,
            mcpDir,
            manualStartCommand: `cd "${mcpDir}" && npm install && npm run build && node dist/server.js`,
            message: "MCP server generated. Start it manually using the command above.",
            chatUrl: `/chat?mcpServerUrl=http://localhost:${port}`,
            playgroundUrl: `/playground?mcpServerUrl=http://localhost:${port}`
        });
    }
    catch (err) {
        res.status(500).json({ error: err.message || err.toString() });
    }
});
// Helper to run shell commands
function runCmd(cmd, args, opts) {
    return new Promise((resolve, reject) => {
        const p = (0, child_process_1.spawn)(cmd, args, { ...opts, stdio: 'inherit', shell: true });
        p.on('exit', code => (code === 0 ? resolve() : reject(new Error(`${cmd} failed`))));
    });
}
exports.default = router;
//# sourceMappingURL=instantMcp.js.map