"use strict";
/**
 * Request validation middleware
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateConvertRequest = validateConvertRequest;
function validateConvertRequest(req, res, next) {
    const { openapi, config } = req.body;
    // Check if openapi is provided
    if (!openapi) {
        res.status(400).json({
            error: 'Missing OpenAPI specification',
            message: 'Request must include an "openapi" field with the specification'
        });
        return;
    }
    // Validate openapi format
    if (typeof openapi !== 'string' && typeof openapi !== 'object') {
        res.status(400).json({
            error: 'Invalid OpenAPI format',
            message: 'OpenAPI specification must be a string (URL or YAML/JSON) or JSON object'
        });
        return;
    }
    // Validate config if provided
    if (config && typeof config !== 'object') {
        res.status(400).json({
            error: 'Invalid config format',
            message: 'Config must be an object'
        });
        return;
    }
    // Validate config fields if provided
    if (config) {
        if (config.port && (typeof config.port !== 'number' || config.port < 1 || config.port > 65535)) {
            res.status(400).json({
                error: 'Invalid port',
                message: 'Port must be a number between 1 and 65535'
            });
            return;
        }
        if (config.name && typeof config.name !== 'string') {
            res.status(400).json({
                error: 'Invalid name',
                message: 'Name must be a string'
            });
            return;
        }
        if (config.version && typeof config.version !== 'string') {
            res.status(400).json({
                error: 'Invalid version',
                message: 'Version must be a string'
            });
            return;
        }
        if (config.baseUrl && typeof config.baseUrl !== 'string') {
            res.status(400).json({
                error: 'Invalid baseUrl',
                message: 'Base URL must be a string'
            });
            return;
        }
    }
    next();
}
exports.default = validateConvertRequest;
//# sourceMappingURL=validation.js.map