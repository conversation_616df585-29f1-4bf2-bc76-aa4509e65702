/**
 * Core type definitions for OpenAPI to MCP conversion
 */
export interface OpenAPISpec {
    openapi: string;
    info: OpenAPIInfo;
    servers?: OpenAPIServer[];
    paths: Record<string, OpenAPIPath>;
    components?: OpenAPIComponents;
    security?: OpenAPISecurityRequirement[];
    tags?: OpenAPITag[];
    externalDocs?: OpenAPIExternalDocumentation;
}
export interface OpenAPIInfo {
    title: string;
    description?: string;
    version: string;
    contact?: {
        name?: string;
        url?: string;
        email?: string;
    };
    license?: {
        name: string;
        url?: string;
    };
}
export interface OpenAPIServer {
    url: string;
    description?: string;
    variables?: Record<string, OpenAPIServerVariable>;
}
export interface OpenAPIServerVariable {
    enum?: string[];
    default: string;
    description?: string;
}
export interface OpenAPIPath {
    summary?: string;
    description?: string;
    get?: OpenAPIOperation;
    post?: OpenAPIOperation;
    put?: OpenAPIOperation;
    patch?: OpenAPIOperation;
    delete?: OpenAPIOperation;
    head?: OpenAPIOperation;
    options?: OpenAPIOperation;
    trace?: OpenAPIOperation;
    parameters?: OpenAPIParameter[];
}
export interface OpenAPIOperation {
    summary?: string;
    description?: string;
    operationId?: string;
    tags?: string[];
    parameters?: OpenAPIParameter[];
    requestBody?: OpenAPIRequestBody;
    responses: Record<string, OpenAPIResponse>;
    security?: OpenAPISecurityRequirement[];
}
export interface OpenAPIParameter {
    name: string;
    in: 'query' | 'header' | 'path' | 'cookie';
    description?: string;
    required?: boolean;
    deprecated?: boolean;
    schema?: OpenAPISchema;
    example?: any;
}
export interface OpenAPIRequestBody {
    description?: string;
    required?: boolean;
    content: Record<string, OpenAPIMediaType>;
}
export interface OpenAPIResponse {
    description: string;
    headers?: Record<string, OpenAPIHeader>;
    content?: Record<string, OpenAPIMediaType>;
}
export interface OpenAPIMediaType {
    schema?: OpenAPISchema;
    example?: any;
    examples?: Record<string, OpenAPIExample>;
}
export interface OpenAPIHeader {
    description?: string;
    required?: boolean;
    schema?: OpenAPISchema;
}
export interface OpenAPIExample {
    summary?: string;
    description?: string;
    value?: any;
    externalValue?: string;
}
export interface OpenAPISchema {
    type?: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object';
    format?: string;
    title?: string;
    description?: string;
    default?: any;
    example?: any;
    enum?: any[];
    items?: OpenAPISchema;
    properties?: Record<string, OpenAPISchema>;
    required?: string[];
    additionalProperties?: boolean | OpenAPISchema;
    allOf?: OpenAPISchema[];
    oneOf?: OpenAPISchema[];
    anyOf?: OpenAPISchema[];
    not?: OpenAPISchema;
    $ref?: string;
}
export interface OpenAPIComponents {
    schemas?: Record<string, OpenAPISchema>;
    responses?: Record<string, OpenAPIResponse>;
    parameters?: Record<string, OpenAPIParameter>;
    examples?: Record<string, OpenAPIExample>;
    requestBodies?: Record<string, OpenAPIRequestBody>;
    headers?: Record<string, OpenAPIHeader>;
    securitySchemes?: Record<string, OpenAPISecurityScheme>;
}
export interface OpenAPISecurityScheme {
    type: 'apiKey' | 'http' | 'oauth2' | 'openIdConnect';
    description?: string;
    name?: string;
    in?: 'query' | 'header' | 'cookie';
    scheme?: string;
    bearerFormat?: string;
    flows?: any;
    openIdConnectUrl?: string;
}
export interface OpenAPISecurityRequirement {
    [name: string]: string[];
}
export interface OpenAPITag {
    name: string;
    description?: string;
    externalDocs?: OpenAPIExternalDocumentation;
}
export interface OpenAPIExternalDocumentation {
    description?: string;
    url: string;
}
export interface MCPTool {
    name: string;
    description: string;
    inputSchema: JSONSchema;
    outputSchema?: JSONSchema;
}
export interface MCPManifest {
    name: string;
    version: string;
    description: string;
    author?: string;
    license?: string;
    tools: MCPTool[];
    server: {
        command: string;
        args?: string[];
        env?: Record<string, string>;
    };
}
export interface JSONSchema {
    $schema?: string;
    type?: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object' | 'null';
    title?: string;
    description?: string;
    default?: any;
    examples?: any[];
    enum?: any[];
    const?: any;
    format?: string;
    pattern?: string;
    minLength?: number;
    maxLength?: number;
    minimum?: number;
    maximum?: number;
    exclusiveMinimum?: number;
    exclusiveMaximum?: number;
    multipleOf?: number;
    items?: JSONSchema | JSONSchema[];
    additionalItems?: JSONSchema;
    properties?: Record<string, JSONSchema>;
    additionalProperties?: boolean | JSONSchema;
    patternProperties?: Record<string, JSONSchema>;
    required?: string[];
    minProperties?: number;
    maxProperties?: number;
    dependencies?: Record<string, JSONSchema | string[]>;
    allOf?: JSONSchema[];
    anyOf?: JSONSchema[];
    oneOf?: JSONSchema[];
    not?: JSONSchema;
    if?: JSONSchema;
    then?: JSONSchema;
    else?: JSONSchema;
    $ref?: string;
    $id?: string;
    $defs?: Record<string, JSONSchema>;
}
export interface CLIOptions {
    input: string;
    output?: string;
    port?: number;
    baseUrl?: string;
    name?: string;
    version?: string;
    author?: string;
    license?: string;
    verbose?: boolean;
    dryRun?: boolean;
}
export interface ServerConfig {
    name: string;
    version: string;
    description: string;
    port: number;
    baseUrl: string;
    outputDir: string;
    author?: string;
    license?: string;
}
export interface ParsedOpenAPI {
    spec: OpenAPISpec;
    baseUrl: string;
    endpoints: ParsedEndpoint[];
}
export interface ParsedEndpoint {
    path: string;
    method: string;
    operationId: string;
    summary?: string;
    description?: string;
    parameters: ParsedParameter[];
    requestBody?: ParsedRequestBody;
    responses: ParsedResponse[];
    tags?: string[];
}
export interface ParsedParameter {
    name: string;
    in: 'query' | 'header' | 'path' | 'cookie';
    description?: string;
    required: boolean;
    schema: JSONSchema;
    example?: any;
}
export interface ParsedRequestBody {
    description?: string;
    required: boolean;
    contentType: string;
    schema: JSONSchema;
}
export interface ParsedResponse {
    status: string;
    description: string;
    contentType?: string;
    schema?: JSONSchema;
}
export declare class OpenAPIParseError extends Error {
    cause?: Error | undefined;
    constructor(message: string, cause?: Error | undefined);
}
export declare class MCPGenerationError extends Error {
    cause?: Error | undefined;
    constructor(message: string, cause?: Error | undefined);
}
export declare class ServerGenerationError extends Error {
    cause?: Error | undefined;
    constructor(message: string, cause?: Error | undefined);
}
export type HTTPMethod = 'get' | 'post' | 'put' | 'patch' | 'delete' | 'head' | 'options' | 'trace';
export interface ValidationResult {
    valid: boolean;
    errors: string[];
    warnings: string[];
}
export interface GenerationResult {
    success: boolean;
    outputPath: string;
    manifest: MCPManifest;
    errors: string[];
    warnings: string[];
}
//# sourceMappingURL=types.d.ts.map