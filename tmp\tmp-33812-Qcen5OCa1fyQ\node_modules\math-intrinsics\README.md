# math-intrinsics <sup>[![Version Badge][npm-version-svg]][package-url]</sup>

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][npm-badge-png]][package-url]

ES Math-related intrinsics and helpers, robustly cached.

 - `abs`
 - `floor`
 - `isFinite`
 - `isInteger`
 - `isNaN`
 - `isNegativeZero`
 - `max`
 - `min`
 - `mod`
 - `pow`
 - `round`
 - `sign`
 - `constants/maxArrayLength`
 - `constants/maxSafeInteger`
 - `constants/maxValue`


## Tests
Simply clone the repo, `npm install`, and run `npm test`

## Security

Please email [@ljharb](https://github.com/ljharb) or see https://tidelift.com/security if you have a potential security vulnerability to report.

[package-url]: https://npmjs.org/package/math-intrinsics
[npm-version-svg]: https://versionbadg.es/es-shims/math-intrinsics.svg
[deps-svg]: https://david-dm.org/es-shims/math-intrinsics.svg
[deps-url]: https://david-dm.org/es-shims/math-intrinsics
[dev-deps-svg]: https://david-dm.org/es-shims/math-intrinsics/dev-status.svg
[dev-deps-url]: https://david-dm.org/es-shims/math-intrinsics#info=devDependencies
[npm-badge-png]: https://nodei.co/npm/math-intrinsics.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/math-intrinsics.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/es-object.svg
[downloads-url]: https://npm-stat.com/charts.html?package=math-intrinsics
[codecov-image]: https://codecov.io/gh/es-shims/math-intrinsics/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/es-shims/math-intrinsics/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/es-shims/math-intrinsics
[actions-url]: https://github.com/es-shims/math-intrinsics/actions
