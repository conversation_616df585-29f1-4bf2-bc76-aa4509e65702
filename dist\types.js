"use strict";
/**
 * Core type definitions for OpenAPI to MCP conversion
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServerGenerationError = exports.MCPGenerationError = exports.OpenAPIParseError = void 0;
// Error Types
class OpenAPIParseError extends Error {
    constructor(message, cause) {
        super(message);
        this.cause = cause;
        this.name = 'OpenAPIParseError';
    }
}
exports.OpenAPIParseError = OpenAPIParseError;
class MCPGenerationError extends Error {
    constructor(message, cause) {
        super(message);
        this.cause = cause;
        this.name = 'MCPGenerationError';
    }
}
exports.MCPGenerationError = MCPGenerationError;
class ServerGenerationError extends Error {
    constructor(message, cause) {
        super(message);
        this.cause = cause;
        this.name = 'ServerGenerationError';
    }
}
exports.ServerGenerationError = ServerGenerationError;
//# sourceMappingURL=types.js.map