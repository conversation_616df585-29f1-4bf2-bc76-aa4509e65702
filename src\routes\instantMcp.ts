import express from 'express';
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
import portfinder from 'portfinder';
import { v4 as uuidv4 } from 'uuid';
import ServerGenerator from '../core/serverGenerator';
import parseOpenAPI from '../core/openapiParser';
import { BundleGenerator } from '../core/bundleGenerator';
import { MCPManifestGenerator } from '../core/mcpManifestGenerator';

const router = express.Router();

router.post('/', async (req, res) => {
  const { openapiUrl } = req.body;
  if (!openapiUrl) return res.status(400).json({ error: 'Missing openapiUrl' });

  try {
    const bundleId = uuidv4();
    console.log('Generated bundleId:', bundleId);

    // Parse OpenAPI specification
    const openapiRaw = await fetch(openapiUrl).then(r => r.text());
    const parser = new parseOpenAPI();
    const isYaml = openapiUrl.endsWith('.yaml') || openapiUrl.endsWith('.yml');
    const parsed = await parser.parseFromString(openapiRaw, isYaml);
    console.log('Parsed OpenAPI spec:', parsed.spec.info?.title);

    // Create server configuration
    const port = await portfinder.getPortPromise();
    const config = {
      name: parsed.spec.info?.title?.replace(/[^a-zA-Z0-9-]/g, '-').toLowerCase() || 'instant-mcp',
      version: parsed.spec.info?.version || '1.0.0',
      port,
      baseUrl: parsed.baseUrl || `http://localhost:${port}`,
      description: parsed.spec.info?.description || 'Instant MCP server',
      license: parsed.spec.info?.license?.name || 'MIT',
      author: parsed.spec.info?.contact?.name || 'openapi-to-mcp',
      outputDir: `/tmp/mcp-bundles/${bundleId}`
    };

    // Generate MCP manifest
    const manifestGenerator = new MCPManifestGenerator();
    const manifest = manifestGenerator.generateManifest(parsed, config);

    // Generate server files
    const serverGen = new ServerGenerator();
    const files = await serverGen.generateServer(parsed, config);

    // Create downloadable bundle
    const bundleGenerator = new BundleGenerator();
    console.log('Creating bundle with ID:', bundleId);
    console.log('Bundle content keys:', Object.keys(files));

    const bundlePath = await bundleGenerator.createBundle(bundleId, {
      manifest,
      files,
      config
    });

    console.log('Bundle created at:', bundlePath);

    // Verify bundle exists
    const exists = await bundleGenerator.bundleExists(bundleId);
    console.log('Bundle exists check:', exists);

    // Get bundle size for response
    const bundleSize = await bundleGenerator.getBundleSize(bundlePath);
    console.log('Bundle size:', bundleSize);

    res.json({
      success: true,
      bundleId,
      instanceId: bundleId, // For backward compatibility
      downloadUrl: `/api/download/${bundleId}`,
      bundleSize,
      message: "MCP server generated successfully! Download the bundle to get started.",
      config: {
        name: config.name,
        version: config.version,
        port: config.port,
        baseUrl: config.baseUrl
      },
      stats: {
        endpoints: Object.keys(parsed.spec.paths || {}).length,
        tools: manifest.tools.length
      }
    });
  } catch (err: any) {
    console.error('Instant MCP error:', err);
    console.error('Error stack:', err.stack);
    res.status(500).json({ error: err.message || err.toString() });
  }
});

export default router;
