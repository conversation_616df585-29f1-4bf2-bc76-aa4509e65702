import express from 'express';
import tmp from 'tmp-promise';
import fs from 'fs-extra';
import path from 'path';
/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-ignore
import portfinder from 'portfinder';
import { spawn } from 'child_process';
import ServerGenerator from '../core/serverGenerator';
import parseOpenAPI from '../core/openapiParser';

const router = express.Router();

// In-memory registry of running MCP servers
const mcpInstances: Record<string, { port: number, child: any, mcpDir: string }> = {};

router.post('/', async (req, res) => {
  const { openapiUrl } = req.body;
  if (!openapiUrl) return res.status(400).json({ error: 'Missing openapiUrl' });

  try {
    // Ensure project-local tmp directory exists
    await fs.ensureDir(path.join(process.cwd(), 'tmp'));
    // 1. Generate MCP server code in a temp dir (project-local to avoid Windows spawn EINVAL)
    const tmpDir = await tmp.dir({ unsafeCleanup: true, tmpdir: path.join(process.cwd(), 'tmp') });
    const mcpDir = tmpDir.path;

    // 2. Parse OpenAPI and generate MCP server files
    const openapiRaw = await fetch(openapiUrl).then(r => r.text());
    const parser = new parseOpenAPI();
    const isYaml = openapiUrl.endsWith('.yaml') || openapiUrl.endsWith('.yml');
    const parsed = await parser.parseFromString(openapiRaw, isYaml);
    const port = await portfinder.getPortPromise();
    const config = {
      name: 'instant-mcp',
      version: '1.0.0',
      port,
      baseUrl: `http://localhost:${port}`,
      description: parsed.spec.info?.description || 'Instant MCP server',
      license: parsed.spec.info?.license?.name || 'MIT',
      author: parsed.spec.info?.contact?.name || 'openapi-to-mcp',
      outputDir: mcpDir
    };
    const serverGen = new ServerGenerator();
    const files = await serverGen.generateServer(parsed, config);

    for (const [relPath, content] of Object.entries(files)) {
      await fs.outputFile(path.join(mcpDir, relPath), String(content));
    }

    // Copy .env.example to .env so the MCP server has environment variables
    await fs.copyFile(path.join(mcpDir, '.env.example'), path.join(mcpDir, '.env'));

    // Cross-platform npm command
    const npmCmd = process.platform === 'win32' ? 'npm.cmd' : 'npm';

    // Debug/logging for spawn EINVAL
    if (!fs.existsSync(mcpDir)) {
      throw new Error(`Temp directory does not exist: ${mcpDir}`);
    }
    const filesInDir = fs.readdirSync(mcpDir);
    console.log('npmCmd:', npmCmd, 'cwd:', mcpDir, 'files:', filesInDir);

    // Extra: check if npmCmd is absolute or in PATH
    const which = require('which');
    try {
      const npmPath = which.sync(npmCmd);
      console.log('Resolved npm path:', npmPath);
    } catch (e) {
      console.error('npmCmd not found in PATH:', npmCmd);
    }

    // 3. Install dependencies
    try {
      await runCmd(npmCmd, ['install'], { cwd: mcpDir });
    } catch (err) {
      console.error('npm install failed:', err);
      throw err;
    }

    // 4. Build
    try {
      await runCmd(npmCmd, ['run', 'build'], { cwd: mcpDir });
    } catch (err) {
      console.error('npm run build failed:', err);
      throw err;
    }

    // Do NOT start the MCP server automatically.
    // Instead, return the path and instructions for manual start.
    const instanceId = path.basename(String(mcpDir));

    res.json({
      instanceId,
      mcpDir,
      manualStartCommand: `cd "${mcpDir}" && npm install && npm run build && node dist/server.js`,
      message: "MCP server generated. Start it manually using the command above.",
      chatUrl: `/chat?mcpServerUrl=http://localhost:${port}`,
      playgroundUrl: `/playground?mcpServerUrl=http://localhost:${port}`
    });
  } catch (err: any) {
    res.status(500).json({ error: err.message || err.toString() });
  }
});

// Helper to run shell commands
function runCmd(cmd: string, args: string[], opts: any) {
  return new Promise<void>((resolve, reject) => {
    const p = spawn(cmd, args, { ...opts, stdio: 'inherit', shell: true });
    p.on('exit', code => (code === 0 ? resolve() : reject(new Error(`${cmd} failed`))));
  });
}

export default router;
