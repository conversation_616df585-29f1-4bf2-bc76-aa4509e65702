version: '3.8'

services:
  # Main OpenAPI to MCP generator service
  openapi-to-mcp:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - REDIS_URL=${REDIS_URL:-redis://redis:6379}
    volumes:
      - generated-servers:/app/generated-servers
      - ./tmp:/app/tmp
    depends_on:
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for session management and server registry
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx reverse proxy for multiple MCP servers
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - openapi-to-mcp
    restart: unless-stopped

  # Example: Petstore MCP Server (auto-generated)
  mcp-petstore:
    build:
      context: ./generated-servers/petstore-mcp
      dockerfile: Dockerfile
    ports:
      - "8001:8000"
    environment:
      - NODE_ENV=production
      - PORT=8000
      - BASE_URL=https://petstore3.swagger.io/api/v3
      - LITELLM_URL=https://litellm-production-744f.up.railway.app/chat/completions
      - LITELLM_MODEL=deepseek-chat
      - LITELLM_API_KEY=${LITELLM_API_KEY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Example: JSONPlaceholder MCP Server (auto-generated)
  mcp-jsonplaceholder:
    build:
      context: ./generated-servers/jsonplaceholder-mcp
      dockerfile: Dockerfile
    ports:
      - "8002:8000"
    environment:
      - NODE_ENV=production
      - PORT=8000
      - BASE_URL=https://jsonplaceholder.typicode.com
      - LITELLM_URL=https://litellm-production-744f.up.railway.app/chat/completions
      - LITELLM_MODEL=deepseek-chat
      - LITELLM_API_KEY=${LITELLM_API_KEY}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  generated-servers:
  redis-data:

networks:
  default:
    name: mcp-network
