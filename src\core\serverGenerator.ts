/**
 * MCP server code generator
 */

import * as fs from 'fs-extra';
import * as path from 'path';
import Handlebars from 'handlebars';
import {
  ParsedOpenAPI,
  ParsedEndpoint,
  ServerConfig,
  ServerGenerationError
} from '../types';
import { 
  convertPathToExpressRoute, 
  extractPathParams, 
  sanitizeIdentifier,
  ensureDir
} from '../utils';

export class ServerGenerator {
  /**
   * Generate complete MCP server with all files
   */
  async generateServer(parsed: ParsedOpenAPI, config: ServerConfig): Promise<Record<string, string>> {
    try {
      const files: Record<string, string> = {};

      // Generate main server file
      files['src/server.ts'] = this.generateServerCode(parsed, config);
      
      // Generate types file
      let typesFile = this.generateTypesFile(parsed);
      // Fix any lingering tags?: array; issues from OpenAPI schemas
      typesFile = typesFile.replace(/tags\?:\s*array;/g, 'tags?: any[];');
      files['src/types.ts'] = typesFile;
      
      // Generate route handlers
      files['src/routes.ts'] = this.generateRoutesFile(parsed, config);
      
      // Generate package.json
      files['package.json'] = this.generatePackageJson(config);
      
      // Generate tsconfig.json
      files['tsconfig.json'] = this.generateTsConfig();
      
      // Generate README for the generated server
      files['README.md'] = this.generateServerReadme(parsed, config);
      
      // Generate environment file
      files['.env.example'] = this.generateEnvFile(config);

      return files;
    } catch (error) {
      throw new ServerGenerationError(
        'Failed to generate MCP server',
        error as Error
      );
    }
  }

  /**
   * Generate main server TypeScript code
   */
  private generateServerCode(parsed: ParsedOpenAPI, config: ServerConfig): string {
    const template = `
import dotenv from 'dotenv';
import * as path from 'path';
import fs from 'fs';

// Try to load .env from both root and dist (for manual runs)
const envPathRoot = path.resolve(__dirname, '../.env');
const envPathDist = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPathRoot)) {
  dotenv.config({ path: envPathRoot });
} else if (fs.existsSync(envPathDist)) {
  dotenv.config({ path: envPathDist });
} else {
  dotenv.config();
}

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import axios from 'axios';
import { router as apiRoutes } from './routes';

const app = express();
const PORT = process.env.PORT || {{port}};
const BASE_URL = process.env.BASE_URL || '{{baseUrl}}';

// Security middleware
app.use(helmet());
app.use(cors());

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Health check
app.get('/health', (req: express.Request, res: express.Response) => {
  res.json({
    status: 'healthy',
    name: '{{name}}',
    version: '{{version}}',
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL
  });
});

// MCP tool endpoints
app.use('/tools', apiRoutes);

// MCP protocol handler
app.post('/mcp', async (req: express.Request, res: express.Response) => {
  try {
    const { tool, parameters } = req.body;

    // Create the full URL for better logging
    const toolUrl = \`http://localhost:\${PORT}/tools/\${tool}\`;

    console.log(\`[MCP] Incoming request for tool: \${tool}\`);
    console.log(\`[MCP] Tool URL: \${toolUrl}\`);
    console.log(\`[MCP] Parameters:\`, JSON.stringify(parameters, null, 2));

    // Route to appropriate tool handler
    console.log(\`[MCP] Making request to: \${toolUrl}\`);
    console.log(\`[MCP] Request method: POST\`);
    console.log(\`[MCP] Request headers: Content-Type: application/json\`);
    console.log(\`[MCP] Request body: \${JSON.stringify(parameters)}\`);

    const response = await fetch(toolUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(parameters)
    });

    console.log(\`[MCP] Response status: \${response.status}\`);
    console.log(\`[MCP] Response URL: \${response.url}\`);

    // Check if response is ok before parsing JSON
    if (!response.ok) {
      const errorText = await response.text();
      console.error(\`[MCP] Tool endpoint error: \${response.status} \${response.statusText}\`);
      console.error(\`[MCP] Failed URL: \${toolUrl}\`);
      console.error(\`[MCP] Original tool name: \${tool}\`);
      console.error(\`[MCP] Error response body: \${errorText}\`);
      return res.status(response.status).json({
        success: false,
        error: \`Tool '\${tool}' request failed with status \${response.status}\`,
        details: errorText,
        debugInfo: {
          originalTool: tool,
          requestedUrl: toolUrl
        }
      });
    }

    const result = await response.json();
    console.log(\`[MCP] Tool response:\`, JSON.stringify(result, null, 2));

    res.json({
      success: true,
      result
    });
  } catch (error: any) {
    console.error('[MCP] Error in MCP handler:', error);
    console.error('[MCP] Error type:', error.constructor.name);
    if (error.cause) {
      console.error('[MCP] Error cause:', error.cause);
    }
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
      errorType: error.constructor.name
    });
  }
});

// LLM Chat endpoint with dynamic OpenAPI tool calling support
app.post('/chat', async (req: express.Request, res: express.Response) => {
  console.log('[MCP CHAT] Received chat request');

  // Accept either { message } or { messages } (chat history)
  let messages = req.body.messages;
  const singleMessage = req.body.message;

  console.log('[MCP CHAT] Message content:', singleMessage || (messages && messages.length > 0 ? messages[messages.length-1].content : 'No message'));

  const headers: Record<string, string> = {};
  if (process.env.LITELLM_API_KEY) {
    headers['Authorization'] = \`Bearer \${process.env.LITELLM_API_KEY}\`;
  }

  const llmUrl = process.env.LITELLM_URL || 'http://localhost:4000/chat/completions';
  const llmModel = process.env.LITELLM_MODEL || 'gpt-3.5-turbo';

  // Dynamically generate tools/functions from OpenAPI endpoints
  const openApiTools = require('./types').openApiTools || [];
  const tools = openApiTools;

  console.log('[MCP CHAT] Available tools:', tools.map((t: any) => t.function.name).join(', '));

  // Enhanced system prompt for better tool calling
  const systemPrompt = \`You are an API assistant for this service. Help users interact with the API using natural language.

IMPORTANT: Always use the available tools/functions to get real-time data. Never provide cached or assumed information.

Available tools/functions:
\${openApiTools.map((tool: any) => \`- \${tool.function.name}: \${tool.function.description}\`).join('\\n')}

When a user asks for information that matches any of these tools, you MUST call the appropriate tool to get current data. Never provide information without calling the tool first.\`;

  // Compose the OpenAI/LiteLLM request with tool calling
  let payloadMessages: any[];
  if (Array.isArray(messages) && messages.length > 0) {
    // Use provided chat history
    payloadMessages = [{ role: 'system', content: systemPrompt }, ...messages];
  } else if (singleMessage) {
    // Fallback to single message
    payloadMessages = [{ role: 'system', content: systemPrompt }, { role: 'user', content: singleMessage }];
  } else {
    return res.status(400).json({ error: 'No message(s) provided' });
  }

  const payload: any = {
    model: llmModel,
    messages: payloadMessages,
    tools,
    tool_choice: "auto"
  };

  console.log('[MCP CHAT] Sending request to LLM:', {
    url: llmUrl,
    model: llmModel,
    apiKeyPresent: !!process.env.LITELLM_API_KEY,
    messageCount: payloadMessages.length,
    toolCount: tools.length
  });

  try {
    const llmRes = await axios.post(llmUrl, payload, { headers });
    console.log('[MCP CHAT] Received response from LLM');

    // Check if the LLM wants to call a function/tool
    const toolCall = llmRes.data?.choices?.[0]?.message?.tool_calls?.[0];
    console.log('[MCP CHAT] Tool call present:', !!toolCall);

    if (toolCall && toolCall.function) {
      const { name, arguments: argsJson } = toolCall.function;
      console.log(\`[MCP CHAT] Tool call detected: \${name}\`);

      let args: any = {};
      try {
        args = JSON.parse(argsJson);
        console.log(\`[MCP CHAT] Parsed arguments:\`, args);
      } catch (e) {
        console.error('[MCP CHAT] Failed to parse arguments:', e);
        args = {};
      }

      // DEBUG LOGGING: Show tool call details
      console.log('[MCP CHAT] Tool call:', { name, argsJson, args });

      // Call the corresponding tool endpoint directly via /mcp
      try {
        console.log('[MCP CHAT] Calling /mcp endpoint with tool:', name);
        const mcpPayload = {
          tool: name,
          parameters: args
        };

        // Log the full request details
        console.log(\`[MCP CHAT] MCP request URL: http://localhost:\${PORT}/mcp\`);
        console.log(\`[MCP CHAT] MCP request payload:\`, JSON.stringify(mcpPayload, null, 2));

        const apiRes = await axios.post(\`http://localhost:\${PORT}/mcp\`, mcpPayload);
        console.log('[MCP CHAT] MCP response status:', apiRes.status);
        console.log('[MCP CHAT] MCP response data:', JSON.stringify(apiRes.data, null, 2));

        // Format the response based on the tool called for better user experience
        let formattedResponse = '';
        if (name.includes('findPets') || name.includes('findByStatus') && apiRes.data.result?.success && apiRes.data.result?.data) {
          const pets = apiRes.data.result.data;
          if (Array.isArray(pets)) {
            // Filter out pets with invalid/garbled names and limit to reasonable names
            const petNames = pets
              .map((pet: any) => pet.name)
              .filter((name: any) => {
                if (!name || typeof name !== 'string') return false;
                // Filter out names that are too long or contain too many special characters
                if (name.length > 50) return false;
                // Filter out names with excessive special characters (likely garbled)
                const specialCharCount = (name.match(/[^a-zA-Z0-9\\s\\-_]/g) || []).length;
                return specialCharCount < name.length * 0.3; // Less than 30% special chars
              })
              .slice(0, 20); // Limit to first 20 pets for readability

            if (petNames.length > 0) {
              formattedResponse = \`Found \${pets.length} pets (showing \${petNames.length} with valid names):\\n\\n\${petNames.map((name: string, index: number) => \`\${index + 1}. \${name}\`).join('\\n')}\`;
            } else {
              formattedResponse = \`Found \${pets.length} pets, but no pets have readable names.\`;
            }
          } else {
            formattedResponse = \`Tool \${name} executed successfully. Data: \${JSON.stringify(apiRes.data.result.data)}\`;
          }
        } else if (apiRes.data.result?.success) {
          formattedResponse = \`Tool \${name} executed successfully. Data: \${JSON.stringify(apiRes.data.result.data)}\`;
        } else {
          formattedResponse = \`Tool \${name} called successfully. Result: \${JSON.stringify(apiRes.data.result)}\`;
        }

        // Return a more detailed response to the client
        return res.json({
          response: formattedResponse,
          toolCall: {
            name,
            parameters: args,
            result: apiRes.data.result
          }
        });
      } catch (apiErr: any) {
        console.error('[MCP CHAT] MCP call failed:', apiErr.message);

        // Log detailed error information
        if (apiErr.response) {
          console.error('[MCP CHAT] Response status:', apiErr.response.status);
          console.error('[MCP CHAT] Response data:', JSON.stringify(apiErr.response.data, null, 2));
        } else if (apiErr.request) {
          console.error('[MCP CHAT] No response received, request details:', apiErr.request);
        } else {
          console.error('[MCP CHAT] Error details:', apiErr);
        }

        // Try direct API call as fallback
        try {
          console.log(\`[MCP CHAT] Trying direct API call for \${name}\`);

          // For pet-related endpoints, try to call the actual API
          if (name.includes('findPets') || name.includes('findByStatus')) {
            const status = args.query?.status || 'available';
            const petStoreUrl = \`\${BASE_URL}/pet/findByStatus?status=\${status}\`;
            console.log(\`[MCP CHAT] Calling API: \${petStoreUrl}\`);

            const petRes = await axios.get(petStoreUrl);
            console.log('[MCP CHAT] API response:', petRes.status);

            // Format the pet names nicely
            const petNames = petRes.data
              .map((pet: any) => pet.name)
              .filter((name: any) => {
                if (!name || typeof name !== 'string') return false;
                // Filter out names that are too long or contain too many special characters
                if (name.length > 50) return false;
                // Filter out names with excessive special characters (likely garbled)
                const specialCharCount = (name.match(/[^a-zA-Z0-9\\s\\-_]/g) || []).length;
                return specialCharCount < name.length * 0.3; // Less than 30% special chars
              })
              .slice(0, 20); // Limit to first 20 pets for readability

            const formattedResponse = petNames.length > 0
              ? \`Found \${petRes.data.length} pets with status "\${status}" (showing \${petNames.length} with valid names):\\n\\n\${petNames.map((name: string, index: number) => \`\${index + 1}. \${name}\`).join('\\n')}\`
              : \`Found \${petRes.data.length} pets with status "\${status}", but no pets have readable names.\`;

            return res.json({
              response: formattedResponse,
              note: "Used direct API call as fallback"
            });
          }

          return res.status(500).json({
            error: 'Tool call failed and no fallback available',
            details: apiErr.message
          });
        } catch (fallbackErr: any) {
          console.error('[MCP CHAT] Fallback API call failed:', fallbackErr.message);

          return res.status(500).json({
            error: 'Tool call failed',
            details: apiErr.message,
            fallbackError: fallbackErr.message
          });
        }
      }
    }

    // Otherwise, return the LLM's message as usual
    const llmMessage = llmRes.data?.choices?.[0]?.message?.content || llmRes.data?.choices?.[0]?.text || JSON.stringify(llmRes.data);
    console.log('[MCP CHAT] Returning LLM message:', llmMessage.substring(0, 100) + '...');

    res.json({ response: llmMessage });
  } catch (err: any) {
    console.error('[MCP CHAT] LLM call failed:', err);
    if (err.response) {
      console.error('[MCP CHAT] Response status:', err.response.status);
      console.error('[MCP CHAT] Response data:', err.response.data);
    }

    res.status(500).json({
      error: 'LLM call failed',
      details: err && (err.response?.data || err.message || err.toString())
    });
  }
});

// Error handling
app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error instanceof Error ? error.message : String(error)
  });
});

// 404 handler
app.use('*', (req: express.Request, res: express.Response) => {
  res.status(404).json({
    error: 'Not found',
    message: \`Route \${req.originalUrl} not found\`
  });
});

// Start server
const server = app.listen(PORT, () => {
  console.log(\`🚀 {{name}} MCP Server running on port \${PORT}\`);
  console.log(\`📖 Health check: http://localhost:\${PORT}/health\`);
  console.log(\`🔧 Base URL: \${BASE_URL}\`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

export default app;
`;

    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate({
      name: config.name,
      version: config.version,
      port: config.port,
      baseUrl: config.baseUrl
    });
  }

  /**
   * Generate routes file with tool handlers
   */
  private generateRoutesFile(parsed: ParsedOpenAPI, config: ServerConfig): string {
    const routes: string[] = [];
    
    for (const endpoint of parsed.endpoints) {
      const routeCode = this.generateRouteHandler(endpoint, config.baseUrl);
      routes.push(routeCode);
    }

    const template = `
import { Router, Request, Response } from 'express';
import axios from 'axios';

const router = Router();
const BASE_URL = process.env.BASE_URL || '{{baseUrl}}';

{{#each routes}}
{{{this}}}

{{/each}}

// Generic LLM alias endpoints for robustness across all APIs
// These handle common LLM mistakes and provide better user experience

// Catch-all for missing endpoints - try to find a close match
router.use('*', async (req: Request, res: Response, next: any) => {
  if (req.method !== 'POST') return next();
  
  const requestedPath = req.path.replace('/tools/', '').replace('/', '');
  
  // Get all actual endpoint names from this router
  const actualEndpoints = router.stack
    .filter((layer: any) => layer.route && layer.route.path !== '*')
    .map((layer: any) => layer.route.path.replace('/', ''));
  
  // Try to find a close match using common patterns
  let bestMatch = null;
  
  // Common LLM naming patterns and their likely targets
  const patterns = [
    // "find" variations
    { pattern: /^(find|list|get|search|query)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes(match.toLowerCase())) },
    
    // "create" variations  
    { pattern: /^(add|insert|new|create)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes('create') || ep.includes('add')) },
    
    // "update" variations
    { pattern: /^(edit|modify|change|update)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes('update') || ep.includes('edit')) },
    
    // "delete" variations
    { pattern: /^(remove|destroy|delete)(.+)$/, target: (match: string) => actualEndpoints.find(ep => ep.includes('delete') || ep.includes('remove')) }
  ];
  
  for (const { pattern, target } of patterns) {
    const match = requestedPath.match(pattern);
    if (match) {
      const baseWord = match[2];
      bestMatch = target(baseWord) || actualEndpoints.find(ep => 
        ep.toLowerCase().includes(baseWord.toLowerCase()) || 
        baseWord.toLowerCase().includes(ep.toLowerCase().split(/(?=[A-Z])/).join('').toLowerCase())
      );
      if (bestMatch) break;
    }
  }
  
  // If no pattern match, try exact substring matching
  if (!bestMatch) {
    bestMatch = actualEndpoints.find(ep => 
      ep.toLowerCase().includes(requestedPath.toLowerCase()) ||
      requestedPath.toLowerCase().includes(ep.toLowerCase())
    );
  }
  
  if (bestMatch) {
    console.log(\`[ALIAS] Redirecting /\${requestedPath} to /\${bestMatch}\`);
    req.url = \`/\${bestMatch}\`;
    // req.path is read-only in Express, so do not assign to it!
    return next('route');
  }
  
  // If no match found, return helpful error with available endpoints
  res.status(404).json({
    error: 'Tool not found',
    message: \`Tool '\${requestedPath}' not found.\`,
    availableTools: actualEndpoints,
    suggestion: \`Did you mean one of: \${actualEndpoints.slice(0, 5).join(', ')}?\`
  });
});

export { router };
export default router;
`;

    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate({
      baseUrl: config.baseUrl,
      routes
    });
  }

  /**
   * Generate individual route handler
   */
  private generateRouteHandler(endpoint: ParsedEndpoint, baseUrl: string): string {
    const toolName = sanitizeIdentifier(endpoint.operationId);
    const method = endpoint.method.toLowerCase();
    const path = endpoint.path;
    const expressPath = convertPathToExpressRoute(path);
    const pathParams = extractPathParams(path);

    const template = `
// {{summary}}
router.post('/{{toolName}}', async (req: Request, res: Response) => {
  try {
    const { {{paramDestructure}} } = req.body;
    
    // Build URL with path parameters
    let url = \`\${BASE_URL}{{path}}\`;
    {{#each pathParams}}
    url = url.replace('{{{name}}}', encodeURIComponent({{name}}));
    {{/each}}
    
    // Prepare request configuration
    const config: any = {
      method: '{{method}}',
      url,
      timeout: 30000
    };
    
    {{#if hasQuery}}
    // Add query parameters
    if (query) {
      config.params = query;
    }
    {{/if}}
    
    {{#if hasHeaders}}
    // Add headers
    if (headers) {
      config.headers = { ...config.headers, ...headers };
    }
    {{/if}}
    
    {{#if hasBody}}
    // Add request body
    if (body) {
      config.data = body;
      config.headers = { 
        ...config.headers, 
        'Content-Type': '{{contentType}}' 
      };
    }
    {{/if}}
    
    // Make API request
    const response = await axios(config);
    
    res.json({
      success: true,
      data: response.data,
      status: response.status,
      headers: response.headers
    });
    
  } catch (error: any) {
    console.error('{{toolName}} error:', error.message);
    
    if (error.response) {
      // API error
      res.status(error.response.status || 500).json({
        success: false,
        error: 'API Error',
        message: error.response.data?.message || error.message,
        status: error.response.status,
        data: error.response.data
      });
    } else {
      // Network or other error
      res.status(500).json({
        success: false,
        error: 'Request Failed',
        message: error.message
      });
    }
  }
});`;

    // Determine parameters
    const params: string[] = [];
    if (pathParams.length > 0) {
      params.push(...pathParams);
    }
    
    const hasQuery = endpoint.parameters.some(p => p.in === 'query');
    const hasHeaders = endpoint.parameters.some(p => p.in === 'header');
    const hasBody = !!endpoint.requestBody;
    
    if (hasQuery) params.push('query');
    if (hasHeaders) params.push('headers');
    if (hasBody) params.push('body');

    const compiledTemplate = Handlebars.compile(template);
    return compiledTemplate({
      toolName,
      method: method.toUpperCase(),
      path,
      pathParams: pathParams.map(name => ({ name })),
      paramDestructure: params.join(', '),
      hasQuery,
      hasHeaders,
      hasBody,
      contentType: endpoint.requestBody?.contentType || 'application/json',
      summary: endpoint.summary || `${method.toUpperCase()} ${path}`
    });
  }

  /**
   * Generate types file
   */
  private generateTypesFile(parsed: ParsedOpenAPI): string {
    // Generate OpenAPI tool definitions for LLM function calling
    const openApiTools = parsed.endpoints.map(endpoint => {
      const params: any = {};
      const required: string[] = [];
      
      // Add path parameters
      for (const param of endpoint.parameters.filter(p => p.in === 'path')) {
        params[param.name] = {
          type: param.schema?.type || 'string',
          description: param.description || `Path parameter: ${param.name}`
        };
        if (param.required) required.push(param.name);
      }
      
      // Add query parameters as a nested object
      const queryParams = endpoint.parameters.filter(p => p.in === 'query');
      if (queryParams.length > 0) {
        const queryProps: any = {};
        for (const param of queryParams) {
          queryProps[param.name] = {
            type: param.schema?.type || 'string',
            description: param.description || `Query parameter: ${param.name}`
          };
        }
        params['query'] = {
          type: 'object',
          description: 'Query parameters',
          properties: queryProps
        };
      }
      
      // Add headers if any
      const headerParams = endpoint.parameters.filter(p => p.in === 'header');
      if (headerParams.length > 0) {
        const headerProps: any = {};
        for (const param of headerParams) {
          headerProps[param.name] = {
            type: 'string',
            description: param.description || `Header: ${param.name}`
          };
        }
        params['headers'] = {
          type: 'object',
          description: 'HTTP headers',
          properties: headerProps
        };
      }
      
      // Add request body if exists
      if (endpoint.requestBody) {
        params['body'] = { 
          type: 'object', 
          description: endpoint.requestBody.description || 'Request body'
        };
        if (endpoint.requestBody.required) required.push('body');
      }

      // Enhance description for better LLM understanding
      let enhancedDescription = endpoint.summary || endpoint.description || `${endpoint.method.toUpperCase()} ${endpoint.path}`;

      // Add specific keywords for common operations to help LLM matching
      if (endpoint.operationId.toLowerCase().includes('find') && endpoint.operationId.toLowerCase().includes('pets')) {
        enhancedDescription += '. Use this to find, list, search, or get pets by their status (available, pending, sold).';
      } else if (endpoint.operationId.toLowerCase().includes('find') && endpoint.operationId.toLowerCase().includes('status')) {
        enhancedDescription += '. Use this to find, list, search, or get items by their status.';
      } else if (endpoint.operationId.toLowerCase().includes('get') && endpoint.operationId.toLowerCase().includes('pet')) {
        enhancedDescription += '. Use this to get, find, or retrieve a specific pet by ID.';
      }

      return {
        type: "function",
        function: {
          name: endpoint.operationId,
          description: enhancedDescription,
          parameters: {
            type: "object",
            properties: params,
            required
          }
        }
      };
    });

    return `
// Generated types for MCP server

export interface ToolRequest {
  [key: string]: any;
}

export interface ToolResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
  status?: number;
}

export interface HealthResponse {
  status: string;
  name: string;
  version: string;
  timestamp: string;
  baseUrl: string;
}

export interface MCPManifest {
  // ...other fields...
  tags?: any[];
}

// OpenAPI tool definitions for LLM function calling
export const openApiTools = ${JSON.stringify(openApiTools, null, 2)};

// OpenAPI derived types
${this.generateEndpointTypes(parsed.endpoints)}
`;
  }

  /**
   * Generate TypeScript types for endpoints
   */
  private generateEndpointTypes(endpoints: ParsedEndpoint[]): string {
    const types: string[] = [];

    for (const endpoint of endpoints) {
      const typeName = sanitizeIdentifier(endpoint.operationId) + 'Request';
      const properties: string[] = [];

      // Path parameters
      const pathParams = endpoint.parameters.filter(p => p.in === 'path');
      if (pathParams.length > 0) {
        for (const param of pathParams) {
          const optional = param.required ? '' : '?';
          properties.push(`  ${param.name}${optional}: string;`);
        }
      }

      // Query parameters
      const queryParams = endpoint.parameters.filter(p => p.in === 'query');
      if (queryParams.length > 0) {
        properties.push('  query?: {');
        for (const param of queryParams) {
          const optional = param.required ? '' : '?';
          const type = param.schema.type || 'any';
          properties.push(`    ${param.name}${optional}: ${type};`);
        }
        properties.push('  };');
      }

      // Headers
      const headerParams = endpoint.parameters.filter(p => p.in === 'header');
      if (headerParams.length > 0) {
        properties.push('  headers?: {');
        for (const param of headerParams) {
          const optional = param.required ? '' : '?';
          properties.push(`    ${param.name}${optional}: string;`);
        }
        properties.push('  };');
      }

      // Request body
      if (endpoint.requestBody) {
        const optional = endpoint.requestBody.required ? '' : '?';
        properties.push(`  body${optional}: any;`);
      }

      // Tags (fix for tags?: array)
      if ((endpoint as any).tags) {
        properties.push('  tags?: any[];');
      }

      if (properties.length > 0) {
        types.push(`export interface ${typeName} {
${properties.join('\n')}
}`);
      }
    }

    return types.join('\n\n');
  }

  /**
   * Generate package.json for the generated server
   */
  private generatePackageJson(config: ServerConfig): string {
    const packageJson = {
      name: config.name,
      version: config.version,
      description: config.description,
      main: 'dist/server.js',
      scripts: {
        build: 'tsc',
        start: 'node dist/server.js',
        dev: 'ts-node src/server.ts',
        test: 'echo "No tests specified" && exit 0'
      },
      dependencies: {
        express: '^4.18.2',
        cors: '^2.8.5',
        helmet: '^7.1.0',
        axios: '^1.6.2',
        dotenv: '^16.3.1'
      },
      devDependencies: {
        '@types/node': '^20.10.4',
        '@types/express': '^4.17.21',
        '@types/cors': '^2.8.17',
        typescript: '^5.3.3',
        'ts-node': '^10.9.1'
      },
      engines: {
        node: '>=18.0.0'
      },
      keywords: ['mcp', 'server', 'api', 'openapi'],
      author: config.author || 'Generated by openapi-to-mcp',
      license: config.license || 'MIT'
    };

    return JSON.stringify(packageJson, null, 2);
  }

  /**
   * Generate TypeScript configuration
   */
  private generateTsConfig(): string {
    const tsConfig = {
      compilerOptions: {
        target: 'ES2020',
        module: 'commonjs',
        lib: ['ES2020'],
        outDir: './dist',
        rootDir: './src',
        strict: true,
        esModuleInterop: true,
        skipLibCheck: true,
        forceConsistentCasingInFileNames: true,
        resolveJsonModule: true,
        declaration: true,
        sourceMap: true
      },
      include: ['src/**/*'],
      exclude: ['node_modules', 'dist']
    };

    return JSON.stringify(tsConfig, null, 2);
  }

  /**
   * Generate README for the generated server
   */
  private generateServerReadme(parsed: ParsedOpenAPI, config: ServerConfig): string {
    const endpoints = parsed.endpoints.map(e => 
      `- **${e.operationId}**: ${e.method.toUpperCase()} ${e.path}${e.summary ? ` - ${e.summary}` : ''}`
    ).join('\n');

    return `# ${config.name}

Generated MCP server from OpenAPI specification.

## Overview

- **Name**: ${config.name}
- **Version**: ${config.version}
- **Description**: ${config.description}
- **Base URL**: ${config.baseUrl}
- **Port**: ${config.port}

## Available Tools

${endpoints}

## Installation

\`\`\`bash
npm install
npm run build
\`\`\`

## Usage

\`\`\`bash
# Development
npm run dev

# Production
npm start
\`\`\`

## Environment Variables

Create a \`.env\` file based on \`.env.example\`:

\`\`\`bash
PORT=${config.port}
BASE_URL=${config.baseUrl}
NODE_ENV=production
\`\`\`

## API Endpoints

### Health Check
\`\`\`
GET /health
\`\`\`

### MCP Protocol
\`\`\`
POST /mcp
Content-Type: application/json

{
  "tool": "toolName",
  "parameters": { ... }
}
\`\`\`

### Direct Tool Access
\`\`\`
POST /tools/{toolName}
Content-Type: application/json

{ "param1": "value1", ... }
\`\`\`

## Generated by

[openapi-to-mcp](https://github.com/yourusername/openapi-to-mcp)
`;
  }

  /**
   * Generate environment file template
   */
  private generateEnvFile(config: ServerConfig): string {
    // Auto-detect common public APIs and set appropriate BASE_URL
    let baseUrl = config.baseUrl;
    if (config.baseUrl.includes('localhost') || config.baseUrl.includes('127.0.0.1')) {
      if (config.name.toLowerCase().includes('petstore') || config.description?.toLowerCase().includes('petstore')) {
        baseUrl = 'https://petstore3.swagger.io/api/v3';
      } else if (config.name.toLowerCase().includes('jsonplaceholder')) {
        baseUrl = 'https://jsonplaceholder.typicode.com';
      }
    }

    return `# Generated MCP Server Environment Configuration

# Server Configuration
PORT=${config.port}
BASE_URL=${baseUrl}
NODE_ENV=development

# API Configuration
# Add any API keys or authentication tokens here
# API_KEY=your_api_key_here
# AUTH_TOKEN=your_auth_token_here

# Logging
LOG_LEVEL=info
LITELLM_URL=https://litellm-production-744f.up.railway.app/chat/completions
LITELLM_MODEL=deepseek-chat
LITELLM_API_KEY=sk-railway-litellm-2024
# CORS Configuration
# CORS_ORIGIN=http://localhost:3000
`;
  }
}

export default ServerGenerator;
