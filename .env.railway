# Railway Environment Configuration
# Copy this to your Railway project environment variables

# Application Configuration
NODE_ENV=production
PORT=3000

# LiteLLM Configuration (for AI chat functionality)
LITELLM_URL=https://litellm-production-744f.up.railway.app/chat/completions
LITELLM_MODEL=deepseek-chat
LITELLM_API_KEY=sk-railway-litellm-2024

# CORS Configuration
CORS_ORIGIN=*

# Server Management
MAX_SERVERS=10
BASE_PORT=8000

# Redis Configuration (if using external Redis)
# REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=info
DEBUG=false

# Optional: Custom domain
# RAILWAY_STATIC_URL=your-custom-domain.com
