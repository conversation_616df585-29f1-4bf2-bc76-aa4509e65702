# Deployment Guide

This guide covers deploying the OpenAPI to MCP Server generator with support for multiple running MCP servers.

## 🚀 Railway Deployment (Recommended)

### Quick Deploy

1. **Install Railway CLI**
   ```bash
   npm install -g @railway/cli
   ```

2. **Login to Railway**
   ```bash
   railway login
   ```

3. **Deploy**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

### Manual Railway Deployment

1. **Create a new Railway project**
   ```bash
   railway new
   ```

2. **Set environment variables**
   ```bash
   railway variables set NODE_ENV=production
   railway variables set PORT=3000
   railway variables set LITELLM_URL=https://litellm-production-744f.up.railway.app/chat/completions
   railway variables set LITELLM_MODEL=deepseek-chat
   railway variables set LITELLM_API_KEY=your-api-key
   ```

3. **Deploy**
   ```bash
   railway up
   ```

## 🐳 Docker Deployment

### Single Container

```bash
# Build the image
docker build -t openapi-to-mcp .

# Run the container
docker run -p 3000:3000 \
  -e NODE_ENV=production \
  -e LITELLM_URL=https://litellm-production-744f.up.railway.app/chat/completions \
  -e LITELLM_MODEL=deepseek-chat \
  -e LITELLM_API_KEY=your-api-key \
  openapi-to-mcp
```

### Docker Compose (Multiple Servers)

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

## 🔧 Server Management API

Once deployed, you can manage multiple MCP servers through the API:

### Create a New MCP Server

```bash
curl -X POST https://your-domain.railway.app/api/servers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "petstore-api",
    "openApiUrl": "https://petstore3.swagger.io/api/v3/openapi.json",
    "baseUrl": "https://petstore3.swagger.io/api/v3"
  }'
```

### List All Servers

```bash
curl https://your-domain.railway.app/api/servers
```

### Access Server Endpoints

Each created server is accessible via proxy:

```bash
# Health check
curl https://your-domain.railway.app/api/servers/{server-id}/proxy/health

# Chat with the server
curl -X POST https://your-domain.railway.app/api/servers/{server-id}/proxy/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "find all available pets"}'

# Direct tool access
curl -X POST https://your-domain.railway.app/api/servers/{server-id}/proxy/mcp \
  -H "Content-Type: application/json" \
  -d '{
    "tool": "findPetsByStatus",
    "parameters": {"query": {"status": "available"}}
  }'
```

## 🌐 Multiple Server Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Railway/Docker Host                       │
├─────────────────────────────────────────────────────────────┤
│  Main Generator Service (Port 3000)                        │
│  ├── Web UI                                                │
│  ├── OpenAPI Converter                                     │
│  ├── Server Manager                                        │
│  └── API Proxy                                             │
├─────────────────────────────────────────────────────────────┤
│  Generated MCP Servers                                      │
│  ├── Petstore MCP (Port 8001)                             │
│  ├── JSONPlaceholder MCP (Port 8002)                      │
│  ├── Custom API MCP (Port 8003)                           │
│  └── ... (up to 10 servers)                               │
├─────────────────────────────────────────────────────────────┤
│  Nginx Reverse Proxy                                       │
│  ├── /api/* → Main Service                                │
│  ├── /mcp/petstore/* → Petstore MCP                       │
│  ├── /mcp/jsonplaceholder/* → JSONPlaceholder MCP         │
│  └── /mcp/custom/* → Custom MCP                           │
└─────────────────────────────────────────────────────────────┘
```

## 🔒 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Main service port | `3000` |
| `LITELLM_URL` | LiteLLM gateway URL | `http://localhost:4000/chat/completions` |
| `LITELLM_MODEL` | LLM model to use | `gpt-3.5-turbo` |
| `LITELLM_API_KEY` | LiteLLM API key | - |
| `REDIS_URL` | Redis connection URL | `redis://localhost:6379` |
| `MAX_SERVERS` | Maximum concurrent servers | `10` |

## 📊 Monitoring

### Health Checks

- Main service: `GET /api/health`
- Individual servers: `GET /api/servers/{id}/health`
- All servers status: `GET /api/servers`

### Logs

```bash
# Railway
railway logs

# Docker Compose
docker-compose logs -f [service-name]

# Individual container
docker logs -f container-name
```

## 🔧 Troubleshooting

### Common Issues

1. **Server won't start**
   - Check port availability
   - Verify environment variables
   - Check logs for errors

2. **Tool calls failing**
   - Verify OpenAPI URL is accessible
   - Check base URL configuration
   - Ensure LiteLLM is configured correctly

3. **Memory issues**
   - Limit number of concurrent servers
   - Monitor resource usage
   - Consider upgrading Railway plan

### Debug Mode

Enable debug logging:

```bash
railway variables set DEBUG=true
railway variables set LOG_LEVEL=debug
```

## 🚀 Scaling

### Railway

- Upgrade to Pro plan for more resources
- Use Railway's auto-scaling features
- Monitor usage in Railway dashboard

### Docker

- Use Docker Swarm or Kubernetes for orchestration
- Implement load balancing with multiple replicas
- Use external Redis for session management

## 🔐 Security

- Always use HTTPS in production
- Set proper CORS origins
- Use environment variables for sensitive data
- Implement rate limiting (included in nginx.conf)
- Regular security updates

## 📚 API Documentation

Once deployed, visit your deployment URL for interactive API documentation and the web interface for managing multiple MCP servers.
