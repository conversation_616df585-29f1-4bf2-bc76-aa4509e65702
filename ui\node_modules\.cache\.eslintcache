[{"D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\index.tsx": "1", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\App.tsx": "2", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\services\\api.ts": "3", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\components\\layout\\Header.tsx": "4", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\components\\layout\\Sidebar.tsx": "5", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\ConversionPage.tsx": "6", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\ChatPage.tsx": "7", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\ToolsPage.tsx": "8", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\PlaygroundPage.tsx": "9", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\types\\index.ts": "10", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\components\\ui\\Button.tsx": "11", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\utils\\index.ts": "12", "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\TestPage.tsx": "13"}, {"size": 288, "mtime": 1749011373098, "results": "14", "hashOfConfig": "15"}, {"size": 1408, "mtime": 1749041676291, "results": "16", "hashOfConfig": "15"}, {"size": 11939, "mtime": 1749016351841, "results": "17", "hashOfConfig": "15"}, {"size": 2183, "mtime": 1749012825765, "results": "18", "hashOfConfig": "15"}, {"size": 3396, "mtime": 1749012859633, "results": "19", "hashOfConfig": "15"}, {"size": 4477, "mtime": 1749044698497, "results": "20", "hashOfConfig": "15"}, {"size": 3567, "mtime": 1749121427154, "results": "21", "hashOfConfig": "15"}, {"size": 534, "mtime": 1749012990800, "results": "22", "hashOfConfig": "15"}, {"size": 4764, "mtime": 1749041468141, "results": "23", "hashOfConfig": "15"}, {"size": 6554, "mtime": 1749011198011, "results": "24", "hashOfConfig": "15"}, {"size": 2496, "mtime": 1749015055825, "results": "25", "hashOfConfig": "15"}, {"size": 11382, "mtime": 1749013555194, "results": "26", "hashOfConfig": "15"}, {"size": 2163, "mtime": 1749014592676, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cyt5c2", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\index.tsx", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\App.tsx", ["67", "68"], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\services\\api.ts", ["69"], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\components\\layout\\Header.tsx", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\components\\layout\\Sidebar.tsx", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\ConversionPage.tsx", ["70", "71", "72", "73", "74", "75", "76", "77"], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\ChatPage.tsx", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\ToolsPage.tsx", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\PlaygroundPage.tsx", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\types\\index.ts", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\components\\ui\\Button.tsx", [], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\utils\\index.ts", ["78"], [], "D:\\repos-personal\\repos\\openapi-to-mcp\\ui\\src\\pages\\TestPage.tsx", [], [], {"ruleId": "79", "severity": 1, "message": "80", "line": 5, "column": 17, "nodeType": "81", "messageId": "82", "endLine": 5, "endColumn": 25}, {"ruleId": "79", "severity": 1, "message": "83", "line": 5, "column": 27, "nodeType": "81", "messageId": "82", "endLine": 5, "endColumn": 36}, {"ruleId": "84", "severity": 1, "message": "85", "line": 356, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 356, "endColumn": 20}, {"ruleId": "79", "severity": 1, "message": "88", "line": 12, "column": 10, "nodeType": "81", "messageId": "82", "endLine": 12, "endColumn": 22}, {"ruleId": "79", "severity": 1, "message": "89", "line": 15, "column": 10, "nodeType": "81", "messageId": "82", "endLine": 15, "endColumn": 20}, {"ruleId": "79", "severity": 1, "message": "90", "line": 16, "column": 9, "nodeType": "81", "messageId": "82", "endLine": 16, "endColumn": 21}, {"ruleId": "79", "severity": 1, "message": "91", "line": 17, "column": 9, "nodeType": "81", "messageId": "82", "endLine": 17, "endColumn": 17}, {"ruleId": "79", "severity": 1, "message": "92", "line": 25, "column": 9, "nodeType": "81", "messageId": "82", "endLine": 25, "endColumn": 19}, {"ruleId": "79", "severity": 1, "message": "93", "line": 40, "column": 9, "nodeType": "81", "messageId": "82", "endLine": 40, "endColumn": 23}, {"ruleId": "79", "severity": 1, "message": "94", "line": 45, "column": 9, "nodeType": "81", "messageId": "82", "endLine": 45, "endColumn": 24}, {"ruleId": "79", "severity": 1, "message": "95", "line": 50, "column": 9, "nodeType": "81", "messageId": "82", "endLine": 50, "endColumn": 30}, {"ruleId": "84", "severity": 1, "message": "85", "line": 448, "column": 3, "nodeType": "86", "messageId": "87", "endLine": 448, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'useState' is defined but never used.", "Identifier", "unusedVar", "'useEffect' is defined but never used.", "no-throw-literal", "Expected an error object to be thrown.", "ThrowStatement", "object", "'selectedFile' is assigned a value but never used.", "'isDragOver' is assigned a value but never used.", "'fileInputRef' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'handleDrop' is assigned a value but never used.", "'handleDragOver' is assigned a value but never used.", "'handleDragLeave' is assigned a value but never used.", "'handleFileInputChange' is assigned a value but never used."]