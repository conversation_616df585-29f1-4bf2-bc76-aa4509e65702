/**
 * MCP server code generator
 */
import { Parsed<PERSON><PERSON><PERSON><PERSON>, ServerConfig } from '../types';
export declare class ServerGenerator {
    /**
     * Generate complete MCP server with all files
     */
    generateServer(parsed: Parsed<PERSON>penAPI, config: ServerConfig): Promise<Record<string, string>>;
    /**
     * Generate main server TypeScript code
     */
    private generateServerCode;
    /**
     * Generate routes file with tool handlers
     */
    private generateRoutesFile;
    /**
     * Generate individual route handler
     */
    private generateRouteHandler;
    /**
     * Generate types file
     */
    private generateTypesFile;
    /**
     * Generate TypeScript types for endpoints
     */
    private generateEndpointTypes;
    /**
     * Generate package.json for the generated server
     */
    private generatePackageJson;
    /**
     * Generate TypeScript configuration
     */
    private generateTsConfig;
    /**
     * Generate README for the generated server
     */
    private generateServerReadme;
    /**
     * Generate environment file template
     */
    private generateEnvFile;
}
export default ServerGenerator;
//# sourceMappingURL=serverGenerator.d.ts.map