import React, { useState, useRef, useEffect } from 'react';
import { Button } from '../components/ui/Button';
import { useLocation } from 'react-router-dom';

interface ChatMessage {
  role: 'user' | 'assistant' | 'server';
  content: string;
}

// Helper to get query param
function useQuery() {
  return new URLSearchParams(useLocation().search);
}

export const ChatPage: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [input, setInput] = useState('');
  const [loading, setLoading] = useState(false);
  const chatEndRef = useRef<HTMLDivElement>(null);

  const query = useQuery();
  // Get MCP server URL from query param, fallback to localhost:3000
  const mcpServerUrl = query.get('mcpServerUrl') || 'http://localhost:3000';

  // Scroll to bottom on new message
  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const sendMessage = async () => {
    if (!input.trim()) return;
    const userMsg: ChatMessage = { role: 'user', content: input };
    setMessages((msgs) => [...msgs, userMsg]);
    setInput('');
    setLoading(true);

    try {
      // Send to MCP server's /chat endpoint
      const res = await fetch(`${mcpServerUrl}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages }), // Send full history
      });
      const data = await res.json();
      setMessages((msgs) => [
        ...msgs,
        { role: 'assistant', content: data.response || JSON.stringify(data) },
      ]);
    } catch (err: any) {
      setMessages((msgs) => [
        ...msgs,
        { role: 'assistant', content: 'Error: ' + (err.message || err.toString()) },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !loading) {
      sendMessage();
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Chat with MCP Server</h1>
      <div className="bg-white rounded shadow p-4 h-96 overflow-y-auto flex flex-col mb-4">
        {messages.length === 0 && (
          <div className="text-gray-400 text-center my-auto">Start chatting with your API...</div>
        )}
        {messages.map((msg, idx) => (
          <div
            key={idx}
            className={`mb-2 flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`px-3 py-2 rounded-lg max-w-xs ${
                msg.role === 'user'
                  ? 'bg-indigo-600 text-white'
                  : 'bg-gray-100 text-gray-900'
              }`}
            >
              {msg.content}
            </div>
          </div>
        ))}
        <div ref={chatEndRef} />
      </div>
      <div className="flex gap-2">
        <input
          type="text"
          className="flex-1 border rounded px-3 py-2"
          placeholder="Type your message..."
          value={input}
          onChange={(e) => setInput(e.target.value)}
          onKeyDown={handleInputKeyDown}
          disabled={loading}
        />
        <Button onClick={sendMessage} disabled={loading || !input.trim()}>
          Send
        </Button>
      </div>
      <div className="text-xs text-gray-400 mt-2">
        (Chatting with MCP server at <code>{mcpServerUrl}/chat</code>)
      </div>
    </div>
  );
};
