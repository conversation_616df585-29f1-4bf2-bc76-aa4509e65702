events {
    worker_connections 1024;
}

http {
    upstream openapi_to_mcp {
        server openapi-to-mcp:3000;
    }

    upstream mcp_petstore {
        server mcp-petstore:8000;
    }

    upstream mcp_jsonplaceholder {
        server mcp-jsonplaceholder:8000;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=chat:10m rate=5r/s;

    server {
        listen 80;
        server_name _;

        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";

        # Main OpenAPI to MCP generator
        location / {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://openapi_to_mcp;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 60s;
        }

        # Petstore MCP Server
        location /mcp/petstore/ {
            limit_req zone=chat burst=10 nodelay;
            rewrite ^/mcp/petstore/(.*) /$1 break;
            proxy_pass http://mcp_petstore;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 60s;
        }

        # JSONPlaceholder MCP Server
        location /mcp/jsonplaceholder/ {
            limit_req zone=chat burst=10 nodelay;
            rewrite ^/mcp/jsonplaceholder/(.*) /$1 break;
            proxy_pass http://mcp_jsonplaceholder;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_timeout 60s;
        }

        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
