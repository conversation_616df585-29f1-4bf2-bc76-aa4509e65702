/**
 * Proxy configuration utilities for corporate networks
 */

import { HttpsProxyAgent } from 'https-proxy-agent';
import { HttpProxyAgent } from 'http-proxy-agent';

export interface ProxyConfig {
  httpProxy?: string;
  httpsProxy?: string;
  noProxy?: string;
}

/**
 * Get proxy configuration from environment variables
 */
export function getProxyConfig(): ProxyConfig {
  return {
    httpProxy: process.env.HTTP_PROXY || process.env.http_proxy,
    httpsProxy: process.env.HTTPS_PROXY || process.env.https_proxy,
    noProxy: process.env.NO_PROXY || process.env.no_proxy
  };
}

/**
 * Check if a URL should bypass proxy based on NO_PROXY settings
 */
export function shouldBypassProxy(url: string, noProxy?: string): boolean {
  if (!noProxy) return false;

  const urlObj = new URL(url);
  const hostname = urlObj.hostname;
  
  const noProxyList = noProxy.split(',').map(item => item.trim().toLowerCase());
  
  for (const pattern of noProxyList) {
    if (!pattern) continue;
    
    // Exact match
    if (hostname === pattern) return true;
    
    // Wildcard match (*.example.com)
    if (pattern.startsWith('*.')) {
      const domain = pattern.slice(2);
      if (hostname.endsWith(domain)) return true;
    }
    
    // Domain suffix match (.example.com)
    if (pattern.startsWith('.')) {
      if (hostname.endsWith(pattern)) return true;
    }
    
    // IP range or localhost
    if (pattern === 'localhost' && (hostname === 'localhost' || hostname === '127.0.0.1')) {
      return true;
    }
  }
  
  return false;
}

/**
 * Create appropriate proxy agent for a given URL
 */
export function createProxyAgent(url: string): any {
  const config = getProxyConfig();
  const urlObj = new URL(url);
  
  // Check if we should bypass proxy
  if (shouldBypassProxy(url, config.noProxy)) {
    return undefined;
  }
  
  const isHttps = urlObj.protocol === 'https:';
  const proxyUrl = isHttps ? config.httpsProxy : config.httpProxy;
  
  if (!proxyUrl) {
    return undefined;
  }
  
  console.log(`[PROXY] Using ${isHttps ? 'HTTPS' : 'HTTP'} proxy: ${proxyUrl} for ${url}`);
  
  try {
    if (isHttps) {
      return new HttpsProxyAgent(proxyUrl);
    } else {
      return new HttpProxyAgent(proxyUrl);
    }
  } catch (error) {
    console.error(`[PROXY] Failed to create proxy agent for ${proxyUrl}:`, error);
    return undefined;
  }
}

/**
 * Get fetch options with proxy agent
 */
export function getFetchOptionsWithProxy(url: string, options: RequestInit = {}): RequestInit {
  const agent = createProxyAgent(url);

  if (agent) {
    return {
      ...options,
      // @ts-ignore - Node.js fetch supports agent option
      agent
    };
  }

  return options;
}

/**
 * Get axios config with proxy agent
 */
export function getAxiosConfigWithProxy(url: string, config: any = {}): any {
  const agent = createProxyAgent(url);

  if (agent) {
    return {
      ...config,
      httpsAgent: agent,
      httpAgent: agent
    };
  }

  return config;
}

/**
 * Create a fetch function with proxy support
 */
export function createProxyFetch() {
  return async (url: string | URL, options: RequestInit = {}): Promise<Response> => {
    const urlString = url.toString();
    const proxyOptions = getFetchOptionsWithProxy(urlString, options);
    
    return fetch(url, proxyOptions);
  };
}

/**
 * Log proxy configuration (for debugging)
 */
export function logProxyConfig(): void {
  const config = getProxyConfig();
  
  console.log('[PROXY] Configuration:');
  console.log(`  HTTP_PROXY: ${config.httpProxy || 'not set'}`);
  console.log(`  HTTPS_PROXY: ${config.httpsProxy || 'not set'}`);
  console.log(`  NO_PROXY: ${config.noProxy || 'not set'}`);
  
  if (!config.httpProxy && !config.httpsProxy) {
    console.log('[PROXY] No proxy configured - direct connections will be used');
  }
}

/**
 * Validate proxy URL format
 */
export function validateProxyUrl(proxyUrl: string): boolean {
  try {
    const url = new URL(proxyUrl);
    return ['http:', 'https:'].includes(url.protocol);
  } catch {
    return false;
  }
}

/**
 * Get proxy info for display purposes
 */
export function getProxyInfo(): {
  enabled: boolean;
  httpProxy?: string;
  httpsProxy?: string;
  noProxy?: string;
} {
  const config = getProxyConfig();
  
  return {
    enabled: !!(config.httpProxy || config.httpsProxy),
    httpProxy: config.httpProxy,
    httpsProxy: config.httpsProxy,
    noProxy: config.noProxy
  };
}
