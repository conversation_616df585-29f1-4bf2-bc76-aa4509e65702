/**
 * OpenAPI specification parser
 */

import * as fs from 'fs-extra';
import * as yaml from 'js-yaml';
import axios from 'axios';
import { createProxyAgent, logProxyConfig } from '../utils/proxy';
import {
  OpenAPISpec,
  ParsedOpenAPI,
  ParsedEndpoint,
  ParsedParameter,
  ParsedRequestBody,
  ParsedResponse,
  HTTPMethod,
  OpenAPIParseError,
  ValidationResult
} from '../types';
import {
  isValidUrl,
  validateOpenAPIVersion,
  extractBaseUrl,
  generateOperationId,
  convertOpenAPISchemaToJSONSchema
} from '../utils';

export class OpenAPIParser {
  /**
   * Parse OpenAPI specification from file
   */
  async parseFromFile(filePath: string): Promise<ParsedOpenAPI> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const isYaml = filePath.endsWith('.yaml') || filePath.endsWith('.yml');
      
      const spec = isYaml ? yaml.load(content) as OpenAPISpec : JSON.parse(content);
      return this.parseSpec(spec);
    } catch (error) {
      throw new OpenAPIParseError(
        `Failed to parse OpenAPI file: ${filePath}`,
        error as Error
      );
    }
  }

  /**
   * Parse OpenAPI specification from URL
   */
  async parseFromURL(url: string): Promise<ParsedOpenAPI> {
    try {
      console.log(`[OpenAPI Parser] Fetching OpenAPI spec from: ${url}`);

      // Log proxy configuration for debugging
      logProxyConfig();

      // Create proxy agent if needed
      const proxyAgent = createProxyAgent(url);

      const axiosConfig: any = {
        timeout: 30000, // Increased timeout for corporate networks
        headers: {
          'Accept': 'application/json, application/x-yaml, text/yaml, application/yaml, text/plain',
          'User-Agent': 'OpenAPI-to-MCP/1.0.0',
          'Cache-Control': 'no-cache'
        },
        validateStatus: (status: number) => status < 500 // Accept 4xx responses for debugging
      };

      // Add proxy agent if available
      if (proxyAgent) {
        axiosConfig.httpsAgent = proxyAgent;
        axiosConfig.httpAgent = proxyAgent;
        console.log(`[PROXY] Using proxy agent for ${url}`);
      }

      console.log(`[OpenAPI Parser] Making request with config:`, {
        timeout: axiosConfig.timeout,
        headers: axiosConfig.headers,
        hasProxy: !!proxyAgent
      });

      const response = await axios.get(url, axiosConfig);

      console.log(`[OpenAPI Parser] Response received:`, {
        status: response.status,
        statusText: response.statusText,
        contentType: response.headers['content-type'],
        contentLength: response.headers['content-length'],
        dataType: typeof response.data,
        dataPreview: typeof response.data === 'string' ? response.data.substring(0, 200) : 'Object'
      });

      let spec: OpenAPISpec;
      const contentType = response.headers['content-type'] || '';

      console.log(`[OpenAPI Parser] Parsing response data...`, {
        contentType,
        dataType: typeof response.data,
        isString: typeof response.data === 'string'
      });

      try {
        if (contentType.includes('yaml') || contentType.includes('yml')) {
          console.log(`[OpenAPI Parser] Parsing as YAML based on content-type`);
          spec = yaml.load(response.data) as OpenAPISpec;
        } else if (typeof response.data === 'string') {
          console.log(`[OpenAPI Parser] Parsing string data as JSON`);
          spec = JSON.parse(response.data);
        } else {
          console.log(`[OpenAPI Parser] Using response data as-is (already parsed)`);
          spec = response.data;
        }
      } catch (parseError) {
        console.error(`[OpenAPI Parser] Failed to parse response:`, parseError);
        console.log(`[OpenAPI Parser] Raw response data:`, response.data);
        throw new OpenAPIParseError(`Failed to parse response: ${parseError}`);
      }

      return this.parseSpec(spec);
    } catch (error) {
      throw new OpenAPIParseError(
        `Failed to fetch OpenAPI spec from URL: ${url}`,
        error as Error
      );
    }
  }

  /**
   * Parse OpenAPI specification from string content
   */
  async parseFromString(content: string, isYaml = false): Promise<ParsedOpenAPI> {
    try {
      const spec = isYaml ? yaml.load(content) as OpenAPISpec : JSON.parse(content);
      return this.parseSpec(spec);
    } catch (error) {
      throw new OpenAPIParseError(
        'Failed to parse OpenAPI content',
        error as Error
      );
    }
  }

  /**
   * Parse OpenAPI specification object
   */
  parseSpec(spec: OpenAPISpec): ParsedOpenAPI {
    console.log(`[OpenAPI Parser] Parsing spec with structure:`, {
      type: typeof spec,
      isNull: spec === null,
      isUndefined: spec === undefined,
      keys: spec ? Object.keys(spec) : 'N/A',
      hasOpenapi: spec ? !!(spec as any).openapi : false,
      hasSwagger: spec ? !!(spec as any).swagger : false,
      openapiValue: spec ? (spec as any).openapi : 'N/A',
      swaggerValue: spec ? (spec as any).swagger : 'N/A'
    });

    // Validate the specification
    const validation = this.validateSpec(spec);
    console.log(`[OpenAPI Parser] Validation result:`, validation);

    if (!validation.valid) {
      throw new OpenAPIParseError(
        `Invalid OpenAPI specification: ${validation.errors.join(', ')}`
      );
    }

    // Extract base URL
    const baseUrl = extractBaseUrl(spec.servers);

    // Parse endpoints
    const endpoints = this.parseEndpoints(spec);

    return {
      spec,
      baseUrl,
      endpoints
    };
  }

  /**
   * Validate OpenAPI specification
   */
  validateSpec(spec: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if spec is an object
    if (!spec || typeof spec !== 'object') {
      errors.push('Specification must be an object');
      return { valid: false, errors, warnings };
    }

    // Check OpenAPI version
    if (!spec.openapi) {
      // Check if this might be a Swagger 2.0 spec
      if (spec.swagger) {
        errors.push(`This appears to be a Swagger 2.0 specification (swagger: ${spec.swagger}). Please convert it to OpenAPI 3.0+ format. Available fields: ${Object.keys(spec).join(', ')}`);
      } else {
        errors.push(`Missing openapi field. This doesn't appear to be a valid OpenAPI 3.0+ specification. Available fields: ${Object.keys(spec).join(', ')}`);
      }
    } else if (!validateOpenAPIVersion(spec.openapi)) {
      errors.push(`Unsupported OpenAPI version: ${spec.openapi}. Supported versions: 3.0.x, 3.1.x`);
    }

    // Check info object
    if (!spec.info) {
      errors.push('Missing info object');
    } else {
      if (!spec.info.title) {
        errors.push('Missing info.title');
      }
      if (!spec.info.version) {
        errors.push('Missing info.version');
      }
    }

    // Check paths object
    if (!spec.paths) {
      errors.push('Missing paths object');
    } else if (typeof spec.paths !== 'object') {
      errors.push('Paths must be an object');
    } else if (Object.keys(spec.paths).length === 0) {
      warnings.push('No paths defined in specification');
    }

    // Validate servers
    if (spec.servers && !Array.isArray(spec.servers)) {
      errors.push('Servers must be an array');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Parse all endpoints from OpenAPI paths
   */
  private parseEndpoints(spec: OpenAPISpec): ParsedEndpoint[] {
    const endpoints: ParsedEndpoint[] = [];

    for (const [path, pathItem] of Object.entries(spec.paths)) {
      if (!pathItem) continue;

      const httpMethods: HTTPMethod[] = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace'];

      for (const method of httpMethods) {
        const operation = pathItem[method];
        if (!operation) continue;

        const endpoint: ParsedEndpoint = {
          path,
          method,
          operationId: operation.operationId || generateOperationId(path, method),
          summary: operation.summary,
          description: operation.description,
          parameters: this.parseParameters(operation.parameters || [], pathItem.parameters || []),
          requestBody: operation.requestBody ? this.parseRequestBody(operation.requestBody) : undefined,
          responses: this.parseResponses(operation.responses),
          tags: operation.tags
        };

        endpoints.push(endpoint);
      }
    }

    return endpoints;
  }

  /**
   * Parse operation parameters
   */
  private parseParameters(
    operationParams: any[] = [],
    pathParams: any[] = []
  ): ParsedParameter[] {
    const allParams = [...pathParams, ...operationParams];
    const parsedParams: ParsedParameter[] = [];

    for (const param of allParams) {
      if (!param.name || !param.in) continue;

      const parsedParam: ParsedParameter = {
        name: param.name,
        in: param.in,
        description: param.description,
        required: param.required || param.in === 'path',
        schema: param.schema ? convertOpenAPISchemaToJSONSchema(param.schema) : { type: 'string' },
        example: param.example
      };

      parsedParams.push(parsedParam);
    }

    return parsedParams;
  }

  /**
   * Parse request body
   */
  private parseRequestBody(requestBody: any): ParsedRequestBody | undefined {
    if (!requestBody.content) return undefined;

    // Find the first supported content type
    const supportedTypes = ['application/json', 'application/x-www-form-urlencoded', 'multipart/form-data'];
    let contentType = 'application/json';
    let mediaType = requestBody.content['application/json'];

    for (const type of supportedTypes) {
      if (requestBody.content[type]) {
        contentType = type;
        mediaType = requestBody.content[type];
        break;
      }
    }

    if (!mediaType) {
      // Use the first available content type
      const firstType = Object.keys(requestBody.content)[0];
      if (firstType) {
        contentType = firstType;
        mediaType = requestBody.content[firstType];
      }
    }

    return {
      description: requestBody.description,
      required: requestBody.required || false,
      contentType,
      schema: mediaType?.schema ? convertOpenAPISchemaToJSONSchema(mediaType.schema) : { type: 'object' }
    };
  }

  /**
   * Parse operation responses
   */
  private parseResponses(responses: any): ParsedResponse[] {
    const parsedResponses: ParsedResponse[] = [];

    for (const [status, response] of Object.entries(responses)) {
      if (!response || typeof response !== 'object') continue;

      const parsedResponse: ParsedResponse = {
        status,
        description: (response as any).description || '',
        contentType: undefined,
        schema: undefined
      };

      // Parse response content
      const content = (response as any).content;
      if (content) {
        // Prefer JSON response
        if (content['application/json']) {
          parsedResponse.contentType = 'application/json';
          parsedResponse.schema = content['application/json'].schema 
            ? convertOpenAPISchemaToJSONSchema(content['application/json'].schema)
            : undefined;
        } else {
          // Use first available content type
          const firstType = Object.keys(content)[0];
          if (firstType) {
            parsedResponse.contentType = firstType;
            parsedResponse.schema = content[firstType].schema
              ? convertOpenAPISchemaToJSONSchema(content[firstType].schema)
              : undefined;
          }
        }
      }

      parsedResponses.push(parsedResponse);
    }

    return parsedResponses;
  }

  /**
   * Extract all tags from the specification
   */
  extractTags(spec: OpenAPISpec): string[] {
    const tags = new Set<string>();

    // Add tags from global tags array
    if (spec.tags) {
      for (const tag of spec.tags) {
        if (tag.name) {
          tags.add(tag.name);
        }
      }
    }

    // Add tags from operations
    for (const pathItem of Object.values(spec.paths)) {
      if (!pathItem) continue;

      const httpMethods: HTTPMethod[] = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace'];
      
      for (const method of httpMethods) {
        const operation = pathItem[method];
        if (operation?.tags) {
          for (const tag of operation.tags) {
            tags.add(tag);
          }
        }
      }
    }

    return Array.from(tags).sort();
  }

  /**
   * Get statistics about the OpenAPI specification
   */
  getSpecStats(parsed: ParsedOpenAPI): {
    endpoints: number;
    methods: Record<string, number>;
    tags: string[];
    hasAuthentication: boolean;
    hasRequestBodies: number;
    hasParameters: number;
  } {
    const stats = {
      endpoints: parsed.endpoints.length,
      methods: {} as Record<string, number>,
      tags: this.extractTags(parsed.spec),
      hasAuthentication: !!(parsed.spec.components?.securitySchemes || parsed.spec.security),
      hasRequestBodies: 0,
      hasParameters: 0
    };

    // Count methods and features
    for (const endpoint of parsed.endpoints) {
      stats.methods[endpoint.method] = (stats.methods[endpoint.method] || 0) + 1;
      
      if (endpoint.requestBody) {
        stats.hasRequestBodies++;
      }
      
      if (endpoint.parameters.length > 0) {
        stats.hasParameters++;
      }
    }

    return stats;
  }
}

// Default export for convenience
export default OpenAPIParser;
