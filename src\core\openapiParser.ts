/**
 * OpenAPI specification parser
 */

import * as fs from 'fs-extra';
import * as yaml from 'js-yaml';
import axios from 'axios';
import {
  OpenAPISpec,
  ParsedOpenAPI,
  ParsedEndpoint,
  ParsedParameter,
  ParsedRequestBody,
  ParsedResponse,
  HTTPMethod,
  OpenAPIParseError,
  ValidationResult
} from '../types';
import {
  isValidUrl,
  validateOpenAPIVersion,
  extractBaseUrl,
  generateOperationId,
  convertOpenAPISchemaToJSONSchema
} from '../utils';

export class OpenAPIParser {
  /**
   * Parse OpenAPI specification from file
   */
  async parseFromFile(filePath: string): Promise<ParsedOpenAPI> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const isYaml = filePath.endsWith('.yaml') || filePath.endsWith('.yml');
      
      const spec = isYaml ? yaml.load(content) as OpenAPISpec : JSON.parse(content);
      return this.parseSpec(spec);
    } catch (error) {
      throw new OpenAPIParseError(
        `Failed to parse OpenAPI file: ${filePath}`,
        error as Error
      );
    }
  }

  /**
   * Parse OpenAPI specification from URL
   */
  async parseFromURL(url: string): Promise<ParsedOpenAPI> {
    try {
      const response = await axios.get(url, {
        timeout: 10000,
        headers: {
          'Accept': 'application/json, application/x-yaml, text/yaml'
        }
      });

      let spec: OpenAPISpec;
      const contentType = response.headers['content-type'] || '';
      
      if (contentType.includes('yaml') || contentType.includes('yml')) {
        spec = yaml.load(response.data) as OpenAPISpec;
      } else {
        spec = typeof response.data === 'string' ? JSON.parse(response.data) : response.data;
      }

      return this.parseSpec(spec);
    } catch (error) {
      throw new OpenAPIParseError(
        `Failed to fetch OpenAPI spec from URL: ${url}`,
        error as Error
      );
    }
  }

  /**
   * Parse OpenAPI specification from string content
   */
  async parseFromString(content: string, isYaml = false): Promise<ParsedOpenAPI> {
    try {
      const spec = isYaml ? yaml.load(content) as OpenAPISpec : JSON.parse(content);
      return this.parseSpec(spec);
    } catch (error) {
      throw new OpenAPIParseError(
        'Failed to parse OpenAPI content',
        error as Error
      );
    }
  }

  /**
   * Parse OpenAPI specification object
   */
  parseSpec(spec: OpenAPISpec): ParsedOpenAPI {
    // Validate the specification
    const validation = this.validateSpec(spec);
    if (!validation.valid) {
      throw new OpenAPIParseError(
        `Invalid OpenAPI specification: ${validation.errors.join(', ')}`
      );
    }

    // Extract base URL
    const baseUrl = extractBaseUrl(spec.servers);

    // Parse endpoints
    const endpoints = this.parseEndpoints(spec);

    return {
      spec,
      baseUrl,
      endpoints
    };
  }

  /**
   * Validate OpenAPI specification
   */
  validateSpec(spec: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if spec is an object
    if (!spec || typeof spec !== 'object') {
      errors.push('Specification must be an object');
      return { valid: false, errors, warnings };
    }

    // Check OpenAPI version
    if (!spec.openapi) {
      errors.push('Missing openapi field');
    } else if (!validateOpenAPIVersion(spec.openapi)) {
      errors.push(`Unsupported OpenAPI version: ${spec.openapi}. Supported versions: 3.0.x, 3.1.x`);
    }

    // Check info object
    if (!spec.info) {
      errors.push('Missing info object');
    } else {
      if (!spec.info.title) {
        errors.push('Missing info.title');
      }
      if (!spec.info.version) {
        errors.push('Missing info.version');
      }
    }

    // Check paths object
    if (!spec.paths) {
      errors.push('Missing paths object');
    } else if (typeof spec.paths !== 'object') {
      errors.push('Paths must be an object');
    } else if (Object.keys(spec.paths).length === 0) {
      warnings.push('No paths defined in specification');
    }

    // Validate servers
    if (spec.servers && !Array.isArray(spec.servers)) {
      errors.push('Servers must be an array');
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Parse all endpoints from OpenAPI paths
   */
  private parseEndpoints(spec: OpenAPISpec): ParsedEndpoint[] {
    const endpoints: ParsedEndpoint[] = [];

    for (const [path, pathItem] of Object.entries(spec.paths)) {
      if (!pathItem) continue;

      const httpMethods: HTTPMethod[] = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace'];

      for (const method of httpMethods) {
        const operation = pathItem[method];
        if (!operation) continue;

        const endpoint: ParsedEndpoint = {
          path,
          method,
          operationId: operation.operationId || generateOperationId(path, method),
          summary: operation.summary,
          description: operation.description,
          parameters: this.parseParameters(operation.parameters || [], pathItem.parameters || []),
          requestBody: operation.requestBody ? this.parseRequestBody(operation.requestBody) : undefined,
          responses: this.parseResponses(operation.responses),
          tags: operation.tags
        };

        endpoints.push(endpoint);
      }
    }

    return endpoints;
  }

  /**
   * Parse operation parameters
   */
  private parseParameters(
    operationParams: any[] = [],
    pathParams: any[] = []
  ): ParsedParameter[] {
    const allParams = [...pathParams, ...operationParams];
    const parsedParams: ParsedParameter[] = [];

    for (const param of allParams) {
      if (!param.name || !param.in) continue;

      const parsedParam: ParsedParameter = {
        name: param.name,
        in: param.in,
        description: param.description,
        required: param.required || param.in === 'path',
        schema: param.schema ? convertOpenAPISchemaToJSONSchema(param.schema) : { type: 'string' },
        example: param.example
      };

      parsedParams.push(parsedParam);
    }

    return parsedParams;
  }

  /**
   * Parse request body
   */
  private parseRequestBody(requestBody: any): ParsedRequestBody | undefined {
    if (!requestBody.content) return undefined;

    // Find the first supported content type
    const supportedTypes = ['application/json', 'application/x-www-form-urlencoded', 'multipart/form-data'];
    let contentType = 'application/json';
    let mediaType = requestBody.content['application/json'];

    for (const type of supportedTypes) {
      if (requestBody.content[type]) {
        contentType = type;
        mediaType = requestBody.content[type];
        break;
      }
    }

    if (!mediaType) {
      // Use the first available content type
      const firstType = Object.keys(requestBody.content)[0];
      if (firstType) {
        contentType = firstType;
        mediaType = requestBody.content[firstType];
      }
    }

    return {
      description: requestBody.description,
      required: requestBody.required || false,
      contentType,
      schema: mediaType?.schema ? convertOpenAPISchemaToJSONSchema(mediaType.schema) : { type: 'object' }
    };
  }

  /**
   * Parse operation responses
   */
  private parseResponses(responses: any): ParsedResponse[] {
    const parsedResponses: ParsedResponse[] = [];

    for (const [status, response] of Object.entries(responses)) {
      if (!response || typeof response !== 'object') continue;

      const parsedResponse: ParsedResponse = {
        status,
        description: (response as any).description || '',
        contentType: undefined,
        schema: undefined
      };

      // Parse response content
      const content = (response as any).content;
      if (content) {
        // Prefer JSON response
        if (content['application/json']) {
          parsedResponse.contentType = 'application/json';
          parsedResponse.schema = content['application/json'].schema 
            ? convertOpenAPISchemaToJSONSchema(content['application/json'].schema)
            : undefined;
        } else {
          // Use first available content type
          const firstType = Object.keys(content)[0];
          if (firstType) {
            parsedResponse.contentType = firstType;
            parsedResponse.schema = content[firstType].schema
              ? convertOpenAPISchemaToJSONSchema(content[firstType].schema)
              : undefined;
          }
        }
      }

      parsedResponses.push(parsedResponse);
    }

    return parsedResponses;
  }

  /**
   * Extract all tags from the specification
   */
  extractTags(spec: OpenAPISpec): string[] {
    const tags = new Set<string>();

    // Add tags from global tags array
    if (spec.tags) {
      for (const tag of spec.tags) {
        if (tag.name) {
          tags.add(tag.name);
        }
      }
    }

    // Add tags from operations
    for (const pathItem of Object.values(spec.paths)) {
      if (!pathItem) continue;

      const httpMethods: HTTPMethod[] = ['get', 'post', 'put', 'patch', 'delete', 'head', 'options', 'trace'];
      
      for (const method of httpMethods) {
        const operation = pathItem[method];
        if (operation?.tags) {
          for (const tag of operation.tags) {
            tags.add(tag);
          }
        }
      }
    }

    return Array.from(tags).sort();
  }

  /**
   * Get statistics about the OpenAPI specification
   */
  getSpecStats(parsed: ParsedOpenAPI): {
    endpoints: number;
    methods: Record<string, number>;
    tags: string[];
    hasAuthentication: boolean;
    hasRequestBodies: number;
    hasParameters: number;
  } {
    const stats = {
      endpoints: parsed.endpoints.length,
      methods: {} as Record<string, number>,
      tags: this.extractTags(parsed.spec),
      hasAuthentication: !!(parsed.spec.components?.securitySchemes || parsed.spec.security),
      hasRequestBodies: 0,
      hasParameters: 0
    };

    // Count methods and features
    for (const endpoint of parsed.endpoints) {
      stats.methods[endpoint.method] = (stats.methods[endpoint.method] || 0) + 1;
      
      if (endpoint.requestBody) {
        stats.hasRequestBodies++;
      }
      
      if (endpoint.parameters.length > 0) {
        stats.hasParameters++;
      }
    }

    return stats;
  }
}

// Default export for convenience
export default OpenAPIParser;
