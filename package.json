{"name": "openapi-to-mcp", "version": "1.0.0", "description": "CLI tool to convert OpenAPI specifications into MCP (Model Context Protocol) manifests and servers", "main": "dist/cli.js", "bin": {"openapi-to-mcp": "./dist/cli.js"}, "scripts": {"build": "tsc", "start": "node dist/server.js", "start:cli": "node dist/cli.js", "dev": "ts-node src/server.ts", "dev:cli": "ts-node src/cli.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["openapi", "swagger", "mcp", "model-context-protocol", "cli", "api", "server-generator"], "author": "OpenAPI-to-MCP Contributors", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/openapi-to-mcp.git"}, "bugs": {"url": "https://github.com/yourusername/openapi-to-mcp/issues"}, "homepage": "https://github.com/yourusername/openapi-to-mcp#readme", "dependencies": {"archiver": "^6.0.1", "axios": "^1.6.2", "chalk": "^4.1.2", "commander": "^11.1.0", "cors": "^2.8.5", "express": "^4.18.2", "fs-extra": "^11.3.0", "get-port": "^7.1.0", "handlebars": "^4.7.8", "helmet": "^7.1.0", "js-yaml": "^4.1.0", "multer": "^1.4.5-lts.1", "ora": "^5.4.1", "portfinder": "^1.0.37", "tmp-promise": "^3.0.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/archiver": "^6.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fs-extra": "^11.0.4", "@types/jest": "^29.5.8", "@types/js-yaml": "^4.0.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/**/*", "templates/**/*", "README.md", "LICENSE"]}