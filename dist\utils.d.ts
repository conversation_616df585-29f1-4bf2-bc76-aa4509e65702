/**
 * Utility functions for OpenAPI to MCP conversion
 */
import { OpenAPIServer, JSONSchema, OpenAPISchema } from './types';
/**
 * Check if a string is a valid URL
 */
export declare function isValidUrl(string: string): boolean;
/**
 * Validate OpenAPI version
 */
export declare function validateOpenAPIVersion(version: string): boolean;
/**
 * Extract base URL from servers array
 */
export declare function extractBaseUrl(servers?: OpenAPIServer[]): string;
/**
 * Generate operation ID from path and method
 */
export declare function generateOperationId(path: string, method: string): string;
/**
 * Convert OpenAPI schema to JSON Schema
 */
export declare function convertOpenAPISchemaToJSONSchema(schema: OpenAPISchema): JSONSchema;
/**
 * Sanitize identifier for use as variable/function names
 */
export declare function sanitizeIdentifier(input: string): string;
/**
 * Convert string to camelCase
 */
export declare function toCamelCase(str: string): string;
/**
 * Generate safe filename from string
 */
export declare function generateSafeFilename(input: string): string;
/**
 * Ensure directory exists
 */
export declare function ensureDir(dirPath: string): Promise<void>;
/**
 * Check if file exists
 */
export declare function fileExists(filePath: string): Promise<boolean>;
/**
 * Convert OpenAPI path to Express route format
 */
export declare function convertPathToExpressRoute(path: string): string;
/**
 * Extract path parameters from OpenAPI path
 */
export declare function extractPathParams(path: string): string[];
/**
 * Format bytes to human readable string
 */
export declare function formatBytes(bytes: number): string;
/**
 * Deep merge two objects
 */
export declare function deepMerge<T>(target: T, source: Partial<T>): T;
/**
 * Generate random ID
 */
export declare function generateId(length?: number): string;
/**
 * Validate email format
 */
export declare function isValidEmail(email: string): boolean;
/**
 * Sleep for specified milliseconds
 */
export declare function sleep(ms: number): Promise<void>;
/**
 * Retry function with exponential backoff
 */
export declare function retry<T>(fn: () => Promise<T>, maxAttempts?: number, baseDelay?: number): Promise<T>;
/**
 * Generate timestamp string
 */
export declare function getTimestamp(): string;
/**
 * Capitalize first letter of string
 */
export declare function capitalize(str: string): string;
/**
 * Convert string to PascalCase
 */
export declare function toPascalCase(str: string): string;
/**
 * Validate JSON Schema
 */
export declare function isValidJSONSchema(schema: any): boolean;
//# sourceMappingURL=utils.d.ts.map