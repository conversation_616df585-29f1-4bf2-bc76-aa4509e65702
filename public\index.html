<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAPI to MCP Server Generator</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        .logo {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        .description {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        .feature {
            padding: 1.5rem;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            text-align: center;
        }
        .feature h3 {
            margin-top: 0;
            color: #2563eb;
        }
        .endpoints {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 8px;
            margin: 2rem 0;
        }
        .endpoint {
            margin: 0.5rem 0;
            font-family: 'Monaco', '<PERSON><PERSON>', monospace;
            font-size: 0.9rem;
        }
        .method {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            color: white;
            font-weight: bold;
            margin-right: 0.5rem;
            min-width: 60px;
            text-align: center;
        }
        .get { background: #10b981; }
        .post { background: #3b82f6; }
        .put { background: #f59e0b; }
        .delete { background: #ef4444; }
        .footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e0e0e0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🔧 OpenAPI to MCP</div>
        <div class="description">
            Convert any OpenAPI specification into a Model Context Protocol (MCP) server
        </div>
    </div>

    <div class="features">
        <div class="feature">
            <h3>🚀 Instant Generation</h3>
            <p>Convert OpenAPI specs to MCP servers in seconds with our REST API</p>
        </div>
        <div class="feature">
            <h3>💬 LLM Integration</h3>
            <p>Built-in chat interface with LiteLLM support for testing your APIs</p>
        </div>
        <div class="feature">
            <h3>🔧 Multiple Servers</h3>
            <p>Manage and run multiple MCP servers simultaneously</p>
        </div>
        <div class="feature">
            <h3>🐳 Docker Ready</h3>
            <p>Deploy to Railway, Docker, or any cloud platform</p>
        </div>
    </div>

    <div class="endpoints">
        <h3>📡 Available API Endpoints</h3>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <span>/api/health</span> - Health check
        </div>
        
        <div class="endpoint">
            <span class="method post">POST</span>
            <span>/api/convert</span> - Convert OpenAPI to MCP server
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <span>/api/download/:bundleId</span> - Download generated server
        </div>
        
        <div class="endpoint">
            <span class="method post">POST</span>
            <span>/api/instant-mcp</span> - Create instant MCP server
        </div>
        
        <div class="endpoint">
            <span class="method get">GET</span>
            <span>/api/servers</span> - List all MCP servers
        </div>
        
        <div class="endpoint">
            <span class="method post">POST</span>
            <span>/api/servers</span> - Create new MCP server
        </div>
        
        <div class="endpoint">
            <span class="method post">POST</span>
            <span>/api/chat</span> - Chat with LLM (requires LiteLLM)
        </div>
    </div>

    <div class="endpoints">
        <h3>🎮 Quick Start</h3>
        <p><strong>1. Convert an OpenAPI spec:</strong></p>
        <pre style="background: #f1f5f9; padding: 1rem; border-radius: 4px; overflow-x: auto;">curl -X POST http://localhost:3000/api/convert \
  -H "Content-Type: application/json" \
  -d '{"openapi": "https://petstore3.swagger.io/api/v3/openapi.json"}'</pre>
        
        <p><strong>2. Create instant MCP server:</strong></p>
        <pre style="background: #f1f5f9; padding: 1rem; border-radius: 4px; overflow-x: auto;">curl -X POST http://localhost:3000/api/instant-mcp \
  -H "Content-Type: application/json" \
  -d '{"openApiUrl": "https://petstore3.swagger.io/api/v3/openapi.json"}'</pre>
    </div>

    <div class="footer">
        <p>🔗 <strong>Web UI:</strong> <a href="/ui">http://localhost:3001/ui</a> (if UI server is running)</p>
        <p>Built with ❤️ for the MCP ecosystem</p>
    </div>
</body>
</html>
