"use strict";
/**
 * CLI entry point for OpenAPI to MCP converter
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const commander_1 = require("commander");
const fs = __importStar(require("fs-extra"));
const path = __importStar(require("path"));
const chalk_1 = __importDefault(require("chalk"));
const ora_1 = __importDefault(require("ora"));
const openapiParser_1 = require("./core/openapiParser");
const mcpManifestGenerator_1 = require("./core/mcpManifestGenerator");
const serverGenerator_1 = require("./core/serverGenerator");
const utils_1 = require("./utils");
const program = new commander_1.Command();
// CLI Configuration
program
    .name('openapi-to-mcp')
    .description('Convert OpenAPI specifications to MCP manifests and servers')
    .version('1.0.0');
// Convert command (default)
program
    .argument('<input>', 'OpenAPI specification file or URL')
    .option('-o, --output <path>', 'Output directory', './mcp-server')
    .option('-n, --name <name>', 'Server name')
    .option('-v, --version <version>', 'Server version', '1.0.0')
    .option('-p, --port <port>', 'Server port', '8000')
    .option('-b, --base-url <url>', 'Base API URL')
    .option('-a, --author <author>', 'Author name')
    .option('-l, --license <license>', 'License', 'MIT')
    .option('--verbose', 'Verbose output', false)
    .option('--dry-run', 'Show what would be generated without creating files', false)
    .action(async (input, options) => {
    await convertCommand(input, options);
});
// Serve command
program
    .command('serve')
    .description('Start the API server')
    .option('-p, --port <port>', 'Server port', '3000')
    .option('--cors-origin <origin>', 'CORS origin', '*')
    .action(async (options) => {
    await serveCommand(options);
});
// Info command
program
    .command('info <input>')
    .description('Show information about an OpenAPI specification')
    .action(async (input) => {
    await infoCommand(input);
});
// Validate command
program
    .command('validate <input>')
    .description('Validate an OpenAPI specification')
    .action(async (input) => {
    await validateCommand(input);
});
/**
 * Main convert command implementation
 */
async function convertCommand(input, options) {
    const spinner = (0, ora_1.default)('Initializing conversion...').start();
    try {
        // Parse input
        spinner.text = 'Parsing OpenAPI specification...';
        const parser = new openapiParser_1.OpenAPIParser();
        let parsed;
        if ((0, utils_1.isValidUrl)(input)) {
            parsed = await parser.parseFromURL(input);
        }
        else if (await (0, utils_1.fileExists)(input)) {
            parsed = await parser.parseFromFile(input);
        }
        else {
            throw new Error(`Input file not found: ${input}`);
        }
        spinner.succeed('OpenAPI specification parsed successfully');
        // Create server configuration
        const config = {
            name: options.name || (0, utils_1.generateSafeFilename)(parsed.spec.info.title) || 'mcp-server',
            version: options.version || parsed.spec.info.version || '1.0.0',
            description: parsed.spec.info.description || 'Generated MCP server',
            port: options.port ? parseInt(options.port) : 8000,
            baseUrl: options.baseUrl || parsed.baseUrl,
            outputDir: path.resolve(options.output || './mcp-server'),
            author: options.author,
            license: options.license || 'MIT'
        };
        if (options.verbose) {
            console.log(chalk_1.default.blue('\nConfiguration:'));
            console.log(JSON.stringify(config, null, 2));
        }
        // Generate MCP manifest
        spinner.start('Generating MCP manifest...');
        const manifestGenerator = new mcpManifestGenerator_1.MCPManifestGenerator();
        const manifest = manifestGenerator.generateManifest(parsed, config);
        spinner.succeed(`Generated ${manifest.tools.length} MCP tools`);
        // Generate server code
        spinner.start('Generating server code...');
        const serverGenerator = new serverGenerator_1.ServerGenerator();
        const generatedFiles = await serverGenerator.generateServer(parsed, config);
        spinner.succeed('Server code generated');
        if (options.dryRun) {
            // Dry run - show what would be generated
            console.log(chalk_1.default.yellow('\n🔍 Dry run - showing what would be generated:\n'));
            console.log(chalk_1.default.blue('Files that would be created:'));
            for (const filePath of Object.keys(generatedFiles)) {
                console.log(chalk_1.default.gray(`  ${path.join(config.outputDir, filePath)}`));
            }
            console.log(chalk_1.default.blue('\nMCP Manifest:'));
            console.log(manifestGenerator.generateSummary(manifest));
            console.log(chalk_1.default.green('\n✅ Dry run completed'));
            return;
        }
        // Write files to disk
        spinner.start('Writing files...');
        await (0, utils_1.ensureDir)(config.outputDir);
        // Write MCP manifest
        await fs.writeFile(path.join(config.outputDir, 'mcp.json'), JSON.stringify(manifest, null, 2));
        // Write all generated files
        for (const [filePath, content] of Object.entries(generatedFiles)) {
            const fullPath = path.join(config.outputDir, filePath);
            await (0, utils_1.ensureDir)(path.dirname(fullPath));
            await fs.writeFile(fullPath, content);
        }
        spinner.succeed('Files written successfully');
        // Success message
        console.log(chalk_1.default.green('\n🎉 Conversion completed successfully!\n'));
        console.log(chalk_1.default.blue('Generated files:'));
        console.log(chalk_1.default.gray(`📁 ${config.outputDir}/`));
        console.log(chalk_1.default.gray(`├── mcp.json`));
        for (const filePath of Object.keys(generatedFiles)) {
            const parts = filePath.split('/');
            const indent = '├── ' + '  '.repeat(parts.length - 1);
            console.log(chalk_1.default.gray(`${indent}${parts[parts.length - 1]}`));
        }
        console.log(chalk_1.default.blue('\nNext steps:'));
        console.log(chalk_1.default.white(`1. cd ${config.outputDir}`));
        console.log(chalk_1.default.white('2. npm install'));
        console.log(chalk_1.default.white('3. npm run build'));
        console.log(chalk_1.default.white('4. npm start'));
        console.log(chalk_1.default.blue('\nServer info:'));
        console.log(chalk_1.default.white(`• Name: ${config.name}`));
        console.log(chalk_1.default.white(`• Version: ${config.version}`));
        console.log(chalk_1.default.white(`• Port: ${config.port}`));
        console.log(chalk_1.default.white(`• Tools: ${manifest.tools.length}`));
        console.log(chalk_1.default.white(`• Base URL: ${config.baseUrl}`));
    }
    catch (error) {
        spinner.fail('Conversion failed');
        console.error(chalk_1.default.red('\n❌ Error:'), error.message);
        if (options.verbose && error instanceof Error) {
            console.error(chalk_1.default.gray(error.stack));
        }
        process.exit(1);
    }
}
/**
 * Serve command implementation
 */
async function serveCommand(options) {
    const port = parseInt(options.port) || 3000;
    console.log(chalk_1.default.blue('🚀 Starting OpenAPI-to-MCP API server...\n'));
    // Set environment variables
    process.env.PORT = port.toString();
    process.env.CORS_ORIGIN = options.corsOrigin || '*';
    try {
        // Import and start the server
        const { default: app } = await Promise.resolve().then(() => __importStar(require('./server')));
        console.log(chalk_1.default.green('✅ Server started successfully!'));
        console.log(chalk_1.default.blue('\nAvailable endpoints:'));
        console.log(chalk_1.default.white(`• GET  http://localhost:${port}/`));
        console.log(chalk_1.default.white(`• GET  http://localhost:${port}/api/health`));
        console.log(chalk_1.default.white(`• POST http://localhost:${port}/api/convert`));
        console.log(chalk_1.default.white(`• POST http://localhost:${port}/api/convert/upload`));
        console.log(chalk_1.default.white(`• GET  http://localhost:${port}/api/download/:bundleId`));
    }
    catch (error) {
        console.error(chalk_1.default.red('❌ Failed to start server:'), error.message);
        process.exit(1);
    }
}
/**
 * Info command implementation
 */
async function infoCommand(input) {
    const spinner = (0, ora_1.default)('Loading OpenAPI specification...').start();
    try {
        const parser = new openapiParser_1.OpenAPIParser();
        let parsed;
        if ((0, utils_1.isValidUrl)(input)) {
            parsed = await parser.parseFromURL(input);
        }
        else if (await (0, utils_1.fileExists)(input)) {
            parsed = await parser.parseFromFile(input);
        }
        else {
            throw new Error(`Input file not found: ${input}`);
        }
        spinner.succeed('OpenAPI specification loaded');
        // Display information
        const spec = parsed.spec;
        const stats = parser.getSpecStats(parsed);
        console.log(chalk_1.default.blue('\n📋 OpenAPI Specification Info\n'));
        console.log(chalk_1.default.white('Basic Information:'));
        console.log(chalk_1.default.gray(`• Title: ${spec.info.title}`));
        console.log(chalk_1.default.gray(`• Version: ${spec.info.version}`));
        console.log(chalk_1.default.gray(`• OpenAPI Version: ${spec.openapi}`));
        if (spec.info.description) {
            console.log(chalk_1.default.gray(`• Description: ${spec.info.description}`));
        }
        console.log(chalk_1.default.white('\nServers:'));
        if (spec.servers && spec.servers.length > 0) {
            for (const server of spec.servers) {
                console.log(chalk_1.default.gray(`• ${server.url}${server.description ? ` - ${server.description}` : ''}`));
            }
        }
        else {
            console.log(chalk_1.default.gray('• No servers defined'));
        }
        console.log(chalk_1.default.white('\nEndpoints:'));
        console.log(chalk_1.default.gray(`• Total: ${stats.endpoints}`));
        for (const [method, count] of Object.entries(stats.methods)) {
            console.log(chalk_1.default.gray(`• ${method.toUpperCase()}: ${count}`));
        }
        if (stats.tags.length > 0) {
            console.log(chalk_1.default.white('\nTags:'));
            for (const tag of stats.tags) {
                console.log(chalk_1.default.gray(`• ${tag}`));
            }
        }
        console.log(chalk_1.default.white('\nFeatures:'));
        console.log(chalk_1.default.gray(`• Has Authentication: ${stats.hasAuthentication ? 'Yes' : 'No'}`));
        console.log(chalk_1.default.gray(`• Endpoints with Request Bodies: ${stats.hasRequestBodies}`));
        console.log(chalk_1.default.gray(`• Endpoints with Parameters: ${stats.hasParameters}`));
        // Show what would be generated
        const manifestGenerator = new mcpManifestGenerator_1.MCPManifestGenerator();
        const tools = manifestGenerator.generateTools(parsed);
        const toolStats = manifestGenerator.getToolStats(tools);
        console.log(chalk_1.default.blue('\n🛠️  MCP Generation Preview\n'));
        console.log(chalk_1.default.white('Would generate:'));
        console.log(chalk_1.default.gray(`• ${toolStats.total} MCP tools`));
        console.log(chalk_1.default.gray(`• ${toolStats.withOutput} tools with output schemas`));
        console.log(chalk_1.default.gray(`• ${toolStats.withRequiredInput} tools with required input`));
        console.log(chalk_1.default.gray(`• ${toolStats.averageInputProperties} average input properties per tool`));
    }
    catch (error) {
        spinner.fail('Failed to load specification');
        console.error(chalk_1.default.red('\n❌ Error:'), error.message);
        process.exit(1);
    }
}
/**
 * Validate command implementation
 */
async function validateCommand(input) {
    const spinner = (0, ora_1.default)('Validating OpenAPI specification...').start();
    try {
        const parser = new openapiParser_1.OpenAPIParser();
        let content;
        if ((0, utils_1.isValidUrl)(input)) {
            spinner.text = 'Fetching specification from URL...';
            const axios = await Promise.resolve().then(() => __importStar(require('axios')));
            const response = await axios.default.get(input);
            content = typeof response.data === 'string' ? response.data : JSON.stringify(response.data);
        }
        else if (await (0, utils_1.fileExists)(input)) {
            content = await fs.readFile(input, 'utf-8');
        }
        else {
            throw new Error(`Input file not found: ${input}`);
        }
        // Parse the specification
        const isYaml = input.endsWith('.yaml') || input.endsWith('.yml') || content.trim().startsWith('openapi:');
        const spec = isYaml ?
            (await Promise.resolve().then(() => __importStar(require('js-yaml')))).load(content) :
            JSON.parse(content);
        // Validate
        const validation = parser.validateSpec(spec);
        if (validation.valid) {
            spinner.succeed('OpenAPI specification is valid');
            if (validation.warnings.length > 0) {
                console.log(chalk_1.default.yellow('\n⚠️  Warnings:'));
                for (const warning of validation.warnings) {
                    console.log(chalk_1.default.yellow(`• ${warning}`));
                }
            }
            console.log(chalk_1.default.green('\n✅ Validation passed!'));
        }
        else {
            spinner.fail('OpenAPI specification is invalid');
            console.log(chalk_1.default.red('\n❌ Validation errors:'));
            for (const error of validation.errors) {
                console.log(chalk_1.default.red(`• ${error}`));
            }
            if (validation.warnings.length > 0) {
                console.log(chalk_1.default.yellow('\n⚠️  Warnings:'));
                for (const warning of validation.warnings) {
                    console.log(chalk_1.default.yellow(`• ${warning}`));
                }
            }
            process.exit(1);
        }
    }
    catch (error) {
        spinner.fail('Validation failed');
        console.error(chalk_1.default.red('\n❌ Error:'), error.message);
        process.exit(1);
    }
}
// Parse command line arguments
program.parse();
//# sourceMappingURL=cli.js.map