{"version": 3, "file": "instantMcp.js", "sourceRoot": "", "sources": ["../../src/routes/instantMcp.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,8DAA8B;AAC9B,wDAA0B;AAC1B,gDAAwB;AACxB,sDAAsD;AACtD,aAAa;AACb,4DAAoC;AACpC,iDAAsC;AACtC,8EAAsD;AACtD,0EAAiD;AAEjD,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAEhC,4CAA4C;AAC5C,MAAM,YAAY,GAAiE,EAAE,CAAC;AAEtF,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAClC,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAChC,IAAI,CAAC,UAAU;QAAE,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC;IAE9E,IAAI,CAAC;QACH,4CAA4C;QAC5C,MAAM,kBAAE,CAAC,SAAS,CAAC,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;QACpD,0FAA0F;QAC1F,MAAM,MAAM,GAAG,MAAM,qBAAG,CAAC,GAAG,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,MAAM,EAAE,cAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;QAC/F,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC;QAE3B,iDAAiD;QACjD,MAAM,UAAU,GAAG,MAAM,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;QAC/D,MAAM,MAAM,GAAG,IAAI,uBAAY,EAAE,CAAC;QAClC,MAAM,MAAM,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC3E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAChE,MAAM,IAAI,GAAG,MAAM,oBAAU,CAAC,cAAc,EAAE,CAAC;QAC/C,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,OAAO;YAChB,IAAI;YACJ,OAAO,EAAE,oBAAoB,IAAI,EAAE;YACnC,WAAW,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,IAAI,oBAAoB;YAClE,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,KAAK;YACjD,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,IAAI,gBAAgB;YAC3D,SAAS,EAAE,MAAM;SAClB,CAAC;QACF,MAAM,SAAS,GAAG,IAAI,yBAAe,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,MAAM,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAE7D,KAAK,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;YACvD,MAAM,kBAAE,CAAC,UAAU,CAAC,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC;QACnE,CAAC;QAED,wEAAwE;QACxE,MAAM,kBAAE,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,cAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QAEhF,6BAA6B;QAC7B,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;QAEhE,iCAAiC;QACjC,IAAI,CAAC,kBAAE,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,kCAAkC,MAAM,EAAE,CAAC,CAAC;QAC9D,CAAC;QACD,MAAM,UAAU,GAAG,kBAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC;QAErE,gDAAgD;QAChD,MAAM,KAAK,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC;QAC/B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,OAAO,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,MAAM,CAAC,CAAC;QACrD,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;QACrD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;YAC1C,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,WAAW;QACX,IAAI,CAAC;YACH,MAAM,MAAM,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YAC5C,MAAM,GAAG,CAAC;QACZ,CAAC;QAED,6CAA6C;QAC7C,8DAA8D;QAC9D,MAAM,UAAU,GAAG,cAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QAEjD,GAAG,CAAC,IAAI,CAAC;YACP,UAAU;YACV,MAAM;YACN,kBAAkB,EAAE,OAAO,MAAM,0DAA0D;YAC3F,OAAO,EAAE,kEAAkE;YAC3E,OAAO,EAAE,uCAAuC,IAAI,EAAE;YACtD,aAAa,EAAE,6CAA6C,IAAI,EAAE;SACnE,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,GAAQ,EAAE,CAAC;QAClB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAG,CAAC,OAAO,IAAI,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;IACjE,CAAC;AACH,CAAC,CAAC,CAAC;AAEH,+BAA+B;AAC/B,SAAS,MAAM,CAAC,GAAW,EAAE,IAAc,EAAE,IAAS;IACpD,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC3C,MAAM,CAAC,GAAG,IAAA,qBAAK,EAAC,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QACvE,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;AACL,CAAC;AAED,kBAAe,MAAM,CAAC"}